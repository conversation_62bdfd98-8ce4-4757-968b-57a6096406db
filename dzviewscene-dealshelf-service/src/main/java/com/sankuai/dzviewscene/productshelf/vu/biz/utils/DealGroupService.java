package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.dianping.cat.Cat;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailDto;
import com.dianping.gateway.client.debug.DEBUG;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.enums.CardTypeEnum;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealThemePlanRequest;
import com.sankuai.dztheme.deal.res.*;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemVipPriceVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.CardItemPriceBottomTagsOpt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemvipprice.MergeCardItemVipPriceOpt;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.DzItemVOStyleUtils;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.biz.enums.CardHoldStatusEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.*;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DzItemVOBuildUtils;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealGroupThemeHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.RichLabel;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class DealGroupService {

    @Resource
    private ConfigUtils configUtils;

    @Resource
    private AtomFacadeService atomFacadeService;

    @Autowired
    private CardItemPriceBottomTagsOpt cardItemPriceBottomTagsOpt;

    @Autowired
    private MergeCardItemVipPriceOpt mergeCardItemVipPriceOpt;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.joy.deal.shelf.constant.config", defaultValue = "{}")
    private ConstantConfig constantConfig;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.new.theme.rpc.greypercent", defaultValue = "0")
    private int greyPercent;

    public static final String SUB_CATEGORY_ATTR_KEY = "temp_sub_category";

    @Resource
    private DealThemeSortService dealThemeSortService;

    private DealGroupThemeHandler dealGroupThemeHandler = new DealGroupThemeHandler();

    public CompletableFuture<List<DealThemeDTO>> getDealThemes(List<Integer> dgIds, long tabIdSelected, Map<String, Object> extParams) {
        DealThemePlanRequest request = new DealThemePlanRequest();
        request.setDealIds(dgIds);
        request.setPlanId(configUtils.getPlanId(tabIdSelected));
        request.setExtParams(extParams);
        DEBUG.log("DealThemePlanRequest", request);
        CompletableFuture<DealThemePlanResponse> dealThemePlanFuture = atomFacadeService.getDealThemePlanResponse(request);
        return dealThemePlanFuture.thenApply(new java.util.function.Function<DealThemePlanResponse, List<DealThemeDTO>>() {
            @Override
            public List<DealThemeDTO> apply(DealThemePlanResponse dealThemePlanResponse) {
                if (dealThemePlanResponse == null || CollectionUtils.isEmpty(dealThemePlanResponse.getDeals())) {
                    return Lists.newArrayList();
                }
                DEBUG.log("dealThemePlanResponse", dealThemePlanResponse);
                return dealThemePlanResponse.getDeals();
            }
        });
    }

    public CompletableFuture<Map<Integer, Integer>> buildDpMtDGIdMapper(List<List<IdMapper>> idMappersList) {
        if (CollectionUtils.isEmpty(idMappersList)) {
            return CompletableFuture.completedFuture(MapUtils.EMPTY_MAP);
        }
        Map<Integer, Integer> dpMtDGIdMapper = new HashMap<>();
        for (List<IdMapper> idMappers : idMappersList) {
            if (CollectionUtils.isEmpty(idMappers)) {
                continue;
            }
            dpMtDGIdMapper.putAll(convertDpMtDGIdMapperFromIdMapper(idMappers));
        }
        DEBUG.log("dpMtDGIdMap", dpMtDGIdMapper);
        return CompletableFuture.completedFuture(dpMtDGIdMapper);
    }

    private Map<Integer, Integer> convertDpMtDGIdMapperFromIdMapper(List<IdMapper> idMappers) {
        Map<Integer, Integer> dealIdsRelationMap = new HashMap<>();
        for (IdMapper idMapper : idMappers) {
            dealIdsRelationMap.put(idMapper.getDpDealGroupID(), idMapper.getMtDealGroupID());
        }
        return dealIdsRelationMap;
    }

    public List<DzItemVO> buildDzItems(int platform, long shopId, List<DealThemeDTO> dealThemes, Map<Integer, DealProductDTO> dealProductDTOMap, int mainCategoryId, CardHoldStatusDTO cardHoldStatus, long navIdSelected, boolean hitBuyTestStyle, boolean hitSaleTestStyle, boolean isLogin, String searchKeyword, boolean isHitHighLight, DouHuResponse subTitleDouHuResponse, DouHuResponse noPicDouHu, List<Integer> summaryDealIds) {
        List<Integer> shopRecommendThemeIds = dealThemeSortService.buildShopRecommendThemeIds(dealThemes, navIdSelected, mainCategoryId);
        List<DzItemVO> productItems = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);
        dealThemes.forEach(dealTheme -> productItems.add(buildDzItem(platform, shopId, dealTheme, dealProductDTOMap.get(dealTheme.getDealId()), summaryDealIds, shopRecommendThemeIds, cardHoldStatus, navIdSelected, index.getAndIncrement(), hitBuyTestStyle, hitSaleTestStyle, isLogin, searchKeyword, isHitHighLight, subTitleDouHuResponse, noPicDouHu)));
        return productItems;
    }


    private RichLabelVO buildActivityRemainSeconds(DealProductDTO dealProductDTO) {
        ProductM productM = dealGroupThemeHandler.buildDealProductM(dealProductDTO);
        return PerfectActivityBuildUtils.buildPerfectActivityRemainSecondsLabel(productM);
    }

    private DzItemVO buildDzItem(int platform, long shopId, DealThemeDTO dealTheme,
                                 DealProductDTO dealProductDTO,
                                 List<Integer> summaryDealIds, List<Integer> shopRecommendThemeIds,
                                 CardHoldStatusDTO cardHoldStatus,
                                 long navIdSelected,
                                 int index,
                                 boolean hitBuyTestStyle,
                                 boolean hitSaleTestStyle,
                                 boolean isLogin,
                                 String searchKeyword,
                                 boolean isHitHighLight,
                                 DouHuResponse subTitleDouHuResponse, DouHuResponse noPicDouHu) {
        ProductM productM = dealGroupThemeHandler.buildDealProductM(dealProductDTO);
        DzItemVO dzItemVO = new DzItemVO();
        dzItemVO.setActivityRemainSeconds(getActivityRemainSeconds(dealTheme.getDealMarketingTagsDTO()));
        dzItemVO.setActivityRemainSecondsLabel(buildActivityRemainSeconds(dealProductDTO));
        if (DouHuUtils.CurrentStrategyUtil.hitMassageNoPic(noPicDouHu)) {
            //无头图
            dzItemVO.setPreTitleTag(getPreTitleTagVO(dealTheme, dealProductDTO, shopRecommendThemeIds, navIdSelected));
        }else {
            //有头图
            dzItemVO.setPic(buildPicArea(dealTheme, dealProductDTO, shopRecommendThemeIds, navIdSelected));
        }
        dzItemVO.setItemId(dealTheme.getDealId());
        dzItemVO.setItemIdL(dealTheme.getDealId());
        //废弃
        dzItemVO.setSalePrice(getComponentSalePrice(productM));
        dzItemVO.setBasePrice(productM.getBasePriceTag());
        //废弃
        dzItemVO.setMarketPrice(getMarketPrice(productM));
        dzItemVO.setJumpUrl(dealTheme.getDealDetailUrl());
        dzItemVO.setBottomTags(buildBottomTags(dealTheme.getDealMarketingTagsDTO(), dealTheme.getDealPinDTO()));
        VipPriceVO vipPriceVO = buildVipPrice(dealTheme.getDealCard(), dealTheme.getPrice());
        boolean userHoldJoyCard = userHoldJoyCard(cardHoldStatus);
        resetPriceIfNeed(dzItemVO, vipPriceVO, userHoldJoyCard);
        //废弃
        dzItemVO.setVipPrice(vipPriceVO);
        //废弃
        dzItemVO.setPriceBottomTags(buildPriceBottomTags(vipPriceVO, platform, dealTheme.getDealMarketingTagsDTO(), isLogin, userHoldJoyCard, dealProductDTO));
        dzItemVO.setSale(buildSale(dealTheme.getDealShopSale()));
        dzItemVO.setRichSale(buildRichSale(dealTheme.getDealShopSale(), hitSaleTestStyle));
        dzItemVO.setPurchase(buildPurchase(dealTheme.getDealShopSale(), dealTheme.getPreSaleDTO(), dealProductDTO));

        boolean isPriorityOrderDeal = isPriorityOrderDeal(dealTheme, shopRecommendThemeIds, navIdSelected, summaryDealIds);
        dzItemVO.setLabs(buildLabs(shopId, dealTheme.getDealId(), cardHoldStatus, dzItemVO.getVipPrice(), index, dzItemVO.getPriceBottomTags(), isPriorityOrderDeal));
        dzItemVO.setBtn(buildDzSimpleButtonVO(hitBuyTestStyle, dealTheme.getDealDetailUrl()));
        //构造标题与副标题
        paddingTitleAndSubTitle(dzItemVO, productM, dealTheme, dealProductDTO, subTitleDouHuResponse);
        dzItemVO.setRichTitle(isHitHighLight ? DzItemVOBuildUtils.buildRichTitleWithKeyWordHighlight(dzItemVO.getTitle(), searchKeyword, platform) : null);
        //价格治理重置优惠和价格信息，后续稳定之后把上面重复的给删除掉
        priceOptimizeReset(dzItemVO, productM, dealTheme.getDealMarketingTagsDTO(), cardHoldStatus, platform);
        return dzItemVO;
    }

    /**
     * 设置标题、副标题
     * 涉及：setProductRichTags、setTitle
     * @param dzItemVO
     * @param productM
     * @param subTitleDouHu
     */
    private void paddingTitleAndSubTitle(DzItemVO dzItemVO, ProductM productM, DealThemeDTO dealTheme, DealProductDTO dealProductDTO, DouHuResponse subTitleDouHu) {
        if (DouHuUtils.CurrentStrategyUtil.hitMassageB(subTitleDouHu)) {
            paddingTitleAndSubTitleWithDouHuB(dzItemVO, productM);
            return;
        }
        if (DouHuUtils.CurrentStrategyUtil.hitMassageC(subTitleDouHu)) {
            paddingTitleAndSubTitleWithDouHuC(dzItemVO, productM);
            return;
        }
        dzItemVO.setTitle(productM.getTitle());
        dzItemVO.setProductTags(buildProductTags(dealTheme.getDealAttributes(), getDealStructDetail(dealProductDTO), dealProductDTO == null ? new ArrayList<>() : dealProductDTO.getAttrs()));
    }

    /**
     * 副标题数据来源于主题 && 颜色变黑
     */
    private void paddingTitleAndSubTitleWithDouHuB(DzItemVO dzItemVO, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealGroupService.paddingTitleAndSubTitleWithDouHuB(DzItemVO,ProductM)");
        dzItemVO.setTitle(productM.getTitle());
        if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
            List<RichLabelVO> productRichTags = productM.getProductTags().stream().map(DzItemVOStyleUtils::convertTag2BlackRichLabelProductTag).collect(Collectors.toList());
            dzItemVO.setProductRichTags(productRichTags);
        }
    }

    /**
     * 副标题数据来源于主题 && 标题与主标题互换
     */
    private void paddingTitleAndSubTitleWithDouHuC(DzItemVO dzItemVO, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealGroupService.paddingTitleAndSubTitleWithDouHuC(DzItemVO,ProductM)");
        //标题为副标题
        String dealName = productM.getTitle();
        if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
            dealName = String.join( "｜", productM.getProductTags());
        }
        dzItemVO.setTitle(dealName);
        //副标题为标题
        List<RichLabelVO> dealProductTags = Lists.newArrayList(DzItemVOStyleUtils.convertTag2GrayRichLabelProductTag(productM.getTitle()));
        dzItemVO.setProductRichTags(dealProductTags);
    }

    /**
     * 价格治理重置优惠和价格信息
     */
    private void priceOptimizeReset(DzItemVO dzItemVO, ProductM productM, DealMarketingInfoDTO marketingInfoDTO, CardHoldStatusDTO cardHoldStatus, int platform) {
        if (!configUtils.isPriceOptimize()) {
            //开关控制是否启用
            return;
        }
        CardM cardM = new CardM();
        paddingUserAndShopCardHoldStatus(cardM, cardHoldStatus);
        //市场价
        dzItemVO.setMarketPrice(buildMarketPrice(productM));
        //售价
        dzItemVO.setSalePrice(getComponentSalePrice(productM, cardM));
        //优惠明细
        dzItemVO.setPriceBottomTags(buildPriceBottomTags(platform, productM, cardM, marketingInfoDTO));
        //卡优惠
        dzItemVO.setVipPrice(buildVipPrice(productM, cardM));
    }

    private void paddingUserAndShopCardHoldStatus(CardM cardM, CardHoldStatusDTO cardHoldStatusDTO) {
        if (cardHoldStatusDTO == null) {
            return;
        }
        cardM.setShopCardList(cardHoldStatusDTO.getShopHasCardTypeList());
        cardM.setUserCardList(cardHoldStatusDTO.getUserHoldCardTypeList());
    }

    private DzSimpleButtonVO buildDzSimpleButtonVO(boolean hitBuyTestStyle, String dealDetailUrl) {
        if (!hitBuyTestStyle) {
            return new DzSimpleButtonVO(ButtonTypeEnums.ARROW.getType());
        }
        DzSimpleButtonVO dzSimpleButtonVO = new DzSimpleButtonVO();
        dzSimpleButtonVO.setName("购买");
        dzSimpleButtonVO.setJumpUrl(dealDetailUrl);
        dzSimpleButtonVO.setType(1);
        return dzSimpleButtonVO;
    }

    /**
     * 有玩乐卡价且用户持有玩乐卡，重置售卖价格和VIP价格，即售卖价：售卖价 - 玩乐卡优惠价；VIP价格不展示；原价不展示
     * @param dzItemVO
     * @param vipPriceVO
     */
    private void resetPriceIfNeed(DzItemVO dzItemVO, VipPriceVO vipPriceVO, boolean userHoldJoyCard) {
        if (vipPriceVO == null) {
            return;
        }
        dzItemVO.setMarketPrice(null);
        if (vipPriceVO.getType() != VipPriceEnums.JOY_CARD.getType() || !userHoldJoyCard){
            return;
        }
        dzItemVO.setSalePrice(vipPriceVO.getPrice());
        vipPriceVO.setPrice(null);
    }

    private boolean userHoldJoyCard(CardHoldStatusDTO cardHoldStatus) {
        return cardHoldStatus != null
                && CollectionUtils.isNotEmpty(cardHoldStatus.getUserHoldCardTypeList())
                && cardHoldStatus.getUserHoldCardTypeList().contains(CardTypeEnum.JOY_CARD.getCode());
    }

    private long getActivityRemainSeconds(DealMarketingInfoDTO dealMarketingTagsDTO) {
        if (dealMarketingTagsDTO == null || dealMarketingTagsDTO.getDealActivity() == null) {
            return 0;
        }
        return dealMarketingTagsDTO.getDealActivity().getRemainingTime();
    }

    private String buildLabs(long shopId, int dealGroupId, CardHoldStatusDTO cardHoldStatus, VipPriceVO vipPrice, int index, List<DzTagVO> priceBottomTags, boolean isPriorityOrderDeal) {
        Map<String, Object> cardHoldStatusMap = new HashMap<>();
        if (cardHoldStatus != null) {
            cardHoldStatusMap.put("membercard_type", ShopUserProfileUtils.buildProfile(cardHoldStatus.getShopHasCardTypeList()));
            cardHoldStatusMap.put("u_profile", ShopUserProfileUtils.buildProfile(cardHoldStatus.getUserHoldCardTypeList()));
        }
        cardHoldStatusMap.put("poi_id", shopId);
        cardHoldStatusMap.put("index", index);
        cardHoldStatusMap.put("deal_id", dealGroupId);
        if (isPriorityOrderDeal) {
            cardHoldStatusMap.put("priority_order", 1);
        }
        if (vipPrice == null) {
            cardHoldStatusMap.put("shelf_type", CardHoldStatusEnums.OFFLINE.getStatus());
            return JsonCodec.encode(cardHoldStatusMap);
        }

        if (vipPrice.getType() == VipPriceEnums.JOY_CARD.getType()) {
            cardHoldStatusMap.put("shelf_type", CardHoldStatusEnums.JOY_CARD.getStatus());
            return JsonCodec.encode(cardHoldStatusMap);
        }

        if (Lists.newArrayList(VipPriceEnums.MEMBER.getType(), VipPriceEnums.MEMBER_DAY.getType()).contains(vipPrice.getType())) {
            cardHoldStatusMap.put("shelf_type", CardHoldStatusEnums.DISCOUNT_CARD.getStatus());
            return JsonCodec.encode(cardHoldStatusMap);
        }
        cardHoldStatusMap.put("shelf_type", CardHoldStatusEnums.OFFLINE.getStatus());
        addPromoInfo(cardHoldStatusMap, priceBottomTags);
        return JsonCodec.encode(cardHoldStatusMap);
    }

    private void addPromoInfo(Map<String, Object> oceanMap, List<DzTagVO> promoList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealGroupService.addPromoInfo(java.util.Map,java.util.List)");
        if (CollectionUtils.isEmpty(promoList)) {
            return;
        }
        DzTagVO promo = promoList.stream().filter(promoTag -> promoTag.getPromoDetail() != null).findFirst().orElse(null);
        if (promo == null) {
            return;
        }
        oceanMap.put("promotion_title", promo.getName());
        if (CollectionUtils.isEmpty(promo.getPromoDetail().getPromoItems())) {
            return;
        }
        oceanMap.put("promotion_array", getPromoInfoList(promo.getPromoDetail().getPromoItems()));
    }

    private List<Map<String, String>> getPromoInfoList(List<DzPromoPerItemVO> promoItems) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealGroupService.getPromoInfoList(java.util.List)");
        return promoItems.stream()
                .filter(promoItem -> StringUtils.isNotEmpty(promoItem.getPromoId()))
                .map(promoItem -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("promotion_type", promoItem.getPromoType() + "");
                    map.put("promotion_id", promoItem.getPromoId() + "");
                    return map;
                }).collect(Collectors.toList());
    }

    private String buildSale(DealShopSaleDTO dealShopSale) {
        if (dealShopSale == null || dealShopSale.getSaleNum() <= 0) {
            return Strings.EMPTY;
        }
        return dealShopSale.getSaleNumStr();
    }

    private String buildRichSale(DealShopSaleDTO dealShopSale, boolean hitSaleTestStyle) {
        if (!hitSaleTestStyle) {
            return Strings.EMPTY;
        }
        if (dealShopSale == null || StringUtils.isBlank(dealShopSale.getSaleNumStr())) {
            return Strings.EMPTY;
        }
        return JsonCodec.encode(new RichLabel(dealShopSale.getSaleNumStr(), ColorUtils.colorFF6633, FrontSizeUtils.front11));
    }

    private RichLabelVO buildPurchase(DealShopSaleDTO dealShopSale, DealPreSaleDTO preSaleDTO, DealProductDTO dealProductDTO) {
        ProductM productM = dealGroupThemeHandler.buildDealProductM(dealProductDTO);
        if(PerfectActivityBuildUtils.isDuringPerfectActivityPreheatTime(productM)) {
            return null;
        }
        if (preSaleDTO != null && StringUtils.isNotEmpty(preSaleDTO.getLatestSaleDesc())) {
            return new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front10, ColorUtils.color999999, preSaleDTO.getLatestSaleDesc());
        }
        if (dealShopSale == null || StringUtils.isEmpty(dealShopSale.getLastSaleTimeStr())) {
            return null;
        }
        return new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front10, ColorUtils.color999999, dealShopSale.getLastSaleTimeStr());
    }

    private List<String> buildProductTags(DealStructDTO dealStructs, DealDetailDto dealDetailDto, List<DealProductAttrDTO> attrs) {
        List<String> productTags = new ArrayList<>();
        if (dealStructs != null) {
            addTagIfNoNull(productTags, getDuration(dealStructs.getStruts(), dealDetailDto));
            addTagIfNoNull(productTags, getCorePartTag(dealStructs.getDealAttributes(), dealDetailDto));
        }
        addTagIfNoNull(productTags, getProjectCategory(attrs));
        return productTags;
    }

    private void addTagIfNoNull(List<String> total, String single) {
        if (StringUtils.isEmpty(single)) {
            return;
        }
        total.add(single);
    }

    private String getProjectCategory(List<DealProductAttrDTO> attrs) {
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        Optional<DealProductAttrDTO> attrOpt = attrs.stream()
                .filter(o-> Objects.equals(o.getName(), SUB_CATEGORY_ATTR_KEY)).findFirst();
        if (!attrOpt.isPresent() || StringUtils.isEmpty(attrOpt.get().getValue())) {
            return null;
        }
        List<String> subCategories = JsonCodec.decode(attrOpt.get().getValue(), new TypeReference<List<String>>(){});
        if (CollectionUtils.isEmpty(subCategories) || subCategories.size() > 1) {
            return null;
        }
        String rawCategory = subCategories.get(0);
        if (Objects.equals(rawCategory, "推拿/按摩（不含精油）")) {
            return "推拿按摩";
        }else if (Objects.equals(rawCategory, "推拿/正骨")) {
            return "推拿正骨";
        }else if (Objects.equals(rawCategory, "精油/SPA")) {
            return "精油SPA";
        }else if (Objects.equals(rawCategory, "修脚/足部护理")) {
            return "足部护理";
        }
        return rawCategory;
    }

    /**
     * 团单副标题 - 核心部位
     * @param dealAttributes
     * @param dealDetailDto
     * @return
     */
    private String getCorePartTag(List<DealAttributeDTO> dealAttributes, DealDetailDto dealDetailDto) {
        //优先取结构化字段内容
        String tagFromStructDetail = FootDealShelfStructDetailUtil.getCorePartTag(dealDetailDto);
        if (StringUtils.isNotEmpty(tagFromStructDetail)) {
            return tagFromStructDetail;
        }
        //其次取团单属性
        if (CollectionUtils.isNotEmpty(dealAttributes)) {
            Optional<DealAttributeDTO> attrOpt = dealAttributes.stream().filter(o-> Objects.equals(o.getName(), ConstantUtils.PEDICURE_CORE_REGIONS)).findFirst();
            if (attrOpt.isPresent() && CollectionUtils.isNotEmpty(attrOpt.get().getValues())) {
                return attrOpt.get().getValues().get(0);
            }
        }
        return null;
    }

    private String getDuration(List<String> durations, DealDetailDto dealDetailDto) {
        String tagFromStruct = FootDealShelfStructDetailUtil.getServiceDurationTag(dealDetailDto);
        if (StringUtils.isNotEmpty(tagFromStruct)) {
            return tagFromStruct;
        }
        if (CollectionUtils.isNotEmpty(durations)) {
            return durations.get(0);
        }
        return Strings.EMPTY;
    }

    @Deprecated
    private List<DzTagVO> buildPriceBottomTags(VipPriceVO vipPriceVO, int platform, DealMarketingInfoDTO dealMarketingInfo, boolean isLogin, boolean userHoldJoyCard, DealProductDTO dealProductDTO) {
        List<DzTagVO> dzTags = new ArrayList<>();
        DzTagVO promo = buildPromo(platform, vipPriceVO, isLogin, userHoldJoyCard, dealProductDTO);
        if (promo != null) {
            dzTags.add(promo);
        }
        if (dealMarketingInfo != null && dealMarketingInfo.getDealReturnCoupon() != null) {
            //返券（不清楚还有没有，老逻辑不敢删）
            dzTags.add(new DzTagVO(ColorUtils.colorFF6633, false, dealMarketingInfo.getDealReturnCoupon().getDesc()));
        }
        //空数组前端也会展示
        return CollectionUtils.isEmpty(dzTags) ? null : dzTags;
    }

    private List<DzTagVO> buildPriceBottomTags(int platform, ProductM productM, CardM cardM, DealMarketingInfoDTO dealMarketingInfo) {
        ItemPriceBottomTagsVP.Param param = ItemPriceBottomTagsVP.Param.builder().platform(platform).productM(productM).cardM(cardM).build();
        List<DzTagVO> dzTagVOS = cardItemPriceBottomTagsOpt.compute(null, param, null);
        if (dealMarketingInfo != null && dealMarketingInfo.getDealReturnCoupon() != null) {
            //返券（不清楚还有没有，老逻辑不敢删）
            dzTagVOS.add(new DzTagVO(ColorUtils.colorFF6633, false, dealMarketingInfo.getDealReturnCoupon().getDesc()));
        }
        //空数组前端也会展示
        return CollectionUtils.isEmpty(dzTagVOS) ? null : dzTagVOS;
    }

    private DzTagVO buildPromo(int platform, VipPriceVO vipPrice, boolean isLogin, boolean userHoldJoyCard, DealProductDTO dealProductDTO) {
        if (dealProductDTO == null) {
            return null;
        }
        //商户有玩乐卡会员且用户登陆了且用户持有玩乐卡，则不展示普通优惠信息
        if (vipPrice != null && vipPrice.getType() == VipPriceEnums.JOY_CARD.getType() && isLogin && userHoldJoyCard){
            return null;
        }
        return DzPromoUtils.buildDzTagVo(platform, dealProductDTO.getPromoPrices(), dealProductDTO.getProductId());
    }

    private List<RichLabelVO> buildBottomTags(DealMarketingInfoDTO dealMarketingTags, DealPinDTO dealPin) {
        List<RichLabelVO> bottomTags = new ArrayList<>();
        if (dealMarketingTags != null && dealMarketingTags.getDealTimeCard() != null) {
            DealTimeCardDTO dealTimeCardTag = dealMarketingTags.getDealTimeCard();
            bottomTags.add(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front11, ColorUtils.color777777, String.format("%d次卡", dealTimeCardTag.getTimes())));
            bottomTags.add(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front11, ColorUtils.colorFF6633, String.format("¥%s/次", getOnePointPrice(dealTimeCardTag.getPromoPrice()))));
        }
        if (dealPin == null) {
            return bottomTags;
        }
        if (CollectionUtils.isNotEmpty(bottomTags)) {
            bottomTags.add(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front11, ColorUtils.color777777, "|"));
        }
        bottomTags.add(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front11, ColorUtils.color777777, String.format("%s人团", dealPin.getPinPersonNum())));
        bottomTags.add(new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front11, ColorUtils.colorFF6633, String.format("¥%s/人", getOnePointPrice(dealPin.getPrice()))));
        return bottomTags;
    }

    @Deprecated
    private VipPriceVO buildVipPrice(DealCardDTO dealCard, BigDecimal salePrice) {
        if (dealCard == null || dealCard.getPromoTypeEnum() == null) {
            return null;
        }
        VipPriceVO dealPlayCard = buildDealPlayCard(dealCard);
        if (dealPlayCard != null) {
            return dealPlayCard;
        }
        return buildMemberCard(dealCard, salePrice);
    }

    private VipPriceVO buildVipPrice(ProductM productM, CardM cardM) {
        ItemVipPriceVP.Param param = ItemVipPriceVP.Param.builder().productM(productM).cardM(cardM).build();
        MergeCardItemVipPriceOpt.Config config = new MergeCardItemVipPriceOpt.Config();
        config.setShowType(1);
        return mergeCardItemVipPriceOpt.compute(null, param, config);
    }

    private VipPriceVO buildMemberCard(DealCardDTO dealCard, BigDecimal salePrice) {
        if (!isDisplayMemberCardTag(dealCard, salePrice)) {
            return null;
        }
        DzPictureComponentVO tagImg = new DzPictureComponentVO();
        tagImg.setPicUrl(ConstantUtils.MEMBER_CARD_ICON);
        tagImg.setAspectRadio(ConstantUtils.MEMBER_CARD_ICON_ASPECT_RADIO);
        VipPriceVO vipPriceVO = new VipPriceVO();
        vipPriceVO.setPrice(getOnePointPrice(dealCard.getPrice()));
        vipPriceVO.setType(VipPriceEnums.MEMBER.getType());
        if (dealCard.getPromoTypeEnum().getType() == PromoTypeEnum.MEMBER_DAY.getType()) {
            vipPriceVO.setType(VipPriceEnums.MEMBER_DAY.getType());
            tagImg.setAspectRadio(ConstantUtils.MEMBER_DAY_ICON_ASPECT_RADIO);
            tagImg.setPicUrl(ConstantUtils.MEMBER_DAY_ICON);
        }
        vipPriceVO.setTagImg(tagImg);
        return vipPriceVO;
    }

    private boolean isDisplayMemberCardTag(DealCardDTO dealCard, BigDecimal salePrice) {
        if (dealCard.getPromoTypeEnum().getType() != PromoTypeEnum.DISCOUNT_CARD.getType() && dealCard.getPromoTypeEnum().getType() != PromoTypeEnum.MEMBER_DAY.getType()) {
            return false;
        }
        if (salePrice != null && salePrice.compareTo(dealCard.getPrice()) == -1) {
            return false;
        }
        return true;
    }

    private VipPriceVO buildDealPlayCard(DealCardDTO dealCard) {
        if (!hasDealPlayCardPrice(dealCard.getPromoTypeEnum().getType())) {
            return null;
        }
        VipPriceVO vipPriceVO = new VipPriceVO();
        DzPictureComponentVO dzPictureComponent = new DzPictureComponentVO();
        dzPictureComponent.setPicUrl(constantConfig.playCardIcon);
        dzPictureComponent.setAspectRadio(constantConfig.playCardIconAspectRadio);
        vipPriceVO.setTagImg(dzPictureComponent);
        vipPriceVO.setType(VipPriceEnums.JOY_CARD.getType());
        vipPriceVO.setPrice(getOnePointPrice(dealCard.getPrice()));
        return vipPriceVO;
    }

    private boolean hasDealPlayCardPrice(int cardType) {
        if (cardType != PromoTypeEnum.JOY_CARD.getType()) {
            return false;
        }
        return true;
    }

    @Deprecated
    private String getComponentSalePrice(ProductM productM) {
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && StringUtils.isNotEmpty(perfectActivityPrice)) {
            return perfectActivityPrice;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        return productPromoPriceM == null ? productM.getBasePriceTag() : productPromoPriceM.getPromoPriceTag();
    }

    private String getComponentSalePrice(ProductM productM, CardM cardM) {
        //卡
        ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
        if (cardPromo != null && StringUtils.isNotEmpty(cardPromo.getPromoPriceTag())) {
            //有卡优惠 且 卡优惠没有倒挂，才展示卡价
            if (!CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices())) {
                return cardPromo.getPromoPriceTag();
            }
        }
        //玩美季
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (StringUtils.isNotEmpty(perfectActivityPrice) && PerfectActivityBuildUtils.isDuringPerfectActivity(productM)) {
            return perfectActivityPrice;
        }
        //立减
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM != null && StringUtils.isNotEmpty(productPromoPriceM.getPromoPriceTag())) {
            return productPromoPriceM.getPromoPriceTag();
        }
        //团购
        return productM.getBasePriceTag();
    }

    @Deprecated
    private String getMarketPrice(ProductM productM) {
        return productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType()) == null ? productM.getMarketPrice() : "";
    }

    private String buildMarketPrice(ProductM productM) {
        //有立减
        if (productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType()) != null) {
            return "";
        }
        //有卡优惠
        if (CardPromoUtils.hasCardPromo(productM.getPromoPrices())) {
            return "";
        }
        return productM.getMarketPrice();
    }

    private String getOnePointPrice(BigDecimal price) {
        if (price == null) {
            return Strings.EMPTY;
        }
        if (price.doubleValue() <= 0) {
            return ConstantUtils.PRICE_ZERO;
        }
        return price.setScale(ConstantUtils.ONE_SCALE, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString();
    }

    /**
     * 临时先这样写，下迭代排入足疗迁移的需求之后就直接干掉了
     * @param dealTheme
     * @param dealProductDTO
     * @param shopRecommendThemeIds
     * @param navIdSelected
     * @return
     */
    private FloatTagVO getPreTitleTagVO(DealThemeDTO dealTheme, DealProductDTO dealProductDTO,
                                        List<Integer> shopRecommendThemeIds, long navIdSelected) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.DealGroupService.getPreTitleTagVO(com.sankuai.dztheme.deal.res.DealThemeDTO,com.sankuai.dztheme.deal.dto.DealProductDTO,java.util.List,long)");
        PicAreaVO picAreaVO = buildPicArea(dealTheme, dealProductDTO, shopRecommendThemeIds, navIdSelected);
        if (picAreaVO != null && CollectionUtils.isNotEmpty(picAreaVO.getFloatTags())) {
            return picAreaVO.getFloatTags().get(0);
        }
        return null;
    }

    private PicAreaVO buildPicArea(DealThemeDTO dealTheme, DealProductDTO dealProductDTO,
                                   List<Integer> shopRecommendThemeIds, long navIdSelected) {
        if (StringUtils.isEmpty(dealTheme.getHeadPicUrl())) {
            return null;
        }
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(ConstantUtils.ASPECT_RADIO);
        pic.setPicUrl(PictureURLBuilders.toHttpsUrl(dealTheme.getHeadPicUrl(), ConstantUtils.PIC_WIDTH, ConstantUtils.PIC_HEIGHT, PictureURLBuilders.ScaleType.Cut));
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(pic);
        FloatTagVO floatTag = buildFloatTag(dealTheme, shopRecommendThemeIds, navIdSelected);
        if (floatTag == null) {
            return picAreaVO;
        }
        picAreaVO.setFloatTags(Lists.newArrayList(floatTag));
        ProductM productM = dealGroupThemeHandler.buildDealProductM(dealProductDTO);
        if (PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            picAreaVO.setFloatTags(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
        }
        return picAreaVO;
    }

    private FloatTagVO buildFloatTag(DealThemeDTO dealTheme, List<Integer> shopRecommendThemeIds, long navIdSelected) {
        RichLabelVO preSale = buildPreSale(dealTheme.getPreSaleDTO());
        DzPictureComponentVO activity = buildActivityPictureComponent(dealTheme.getDealMarketingTagsDTO());
        RichLabelVO memberDay = buildMemberDay(dealTheme.getDealCard());
        DzPictureComponentVO shopRecommend = buildShopRecommendPictureComponent(dealTheme, shopRecommendThemeIds, navIdSelected);
        FloatTagVO floatTag = new FloatTagVO();
        floatTag.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        if (preSale != null) {
            floatTag.setLabel(preSale);
            floatTag.setBackgroundImg(ConstantUtils.OTHER_BACKGROUND);
            return floatTag;
        }
        if (activity != null) {
            floatTag.setIcon(activity);
            return floatTag;
        }
        if (memberDay != null) {
            floatTag.setLabel(memberDay);
            floatTag.setBackgroundImg(ConstantUtils.OTHER_BACKGROUND);
            return floatTag;
        }
        if (shopRecommend != null) {
            floatTag.setIcon(shopRecommend);
            return floatTag;
        }
        return null;
    }

    private RichLabelVO buildPreSale(DealPreSaleDTO dealPreSale) {
        if (dealPreSale == null || StringUtils.isEmpty(dealPreSale.getDesc())) {
            return null;
        }
        return new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front10, ColorUtils.color777777, dealPreSale.getDesc());
    }

    private DzPictureComponentVO buildActivityPictureComponent(DealMarketingInfoDTO dealMarketingInfo) {
        if (dealMarketingInfo == null || dealMarketingInfo.getDealActivity() == null || StringUtils.isEmpty(dealMarketingInfo.getDealActivity().getUrl())) {
            return null;
        }
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(dealMarketingInfo.getDealActivity().getUrl());
        dzPictureComponentVO.setAspectRadio(ConstantUtils.PIC_TAG_ASPECT_RADIO);
        return dzPictureComponentVO;
    }

    private DzPictureComponentVO buildShopRecommendPictureComponent(DealThemeDTO dealTheme, List<Integer> shopRecommendThemeIds, long navIdSelected) {
        if (navIdSelected != ConstantUtils.DEFALT_SELECTED_TAG_ID || dealTheme == null || dealTheme.getDealRecommend() == null || !shopRecommendThemeIds.contains(dealTheme.getDealId())) {
            return null;
        }
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(ConstantUtils.SHOP_RECOMMEND_BACKGROUND);
        dzPictureComponentVO.setAspectRadio(ConstantUtils.RECOMMEND_BACKGROUND_ICON_ASPECT_RADIO);
        return dzPictureComponentVO;
    }

    private RichLabelVO buildMemberDay(DealCardDTO dealCard) {
        if (dealCard == null || PromoTypeEnum.MEMBER_DAY.getType() != dealCard.getPromoTypeEnum().getType()) {
            return null;
        }
        return new RichLabelVO(RichLabelPositionEnums.MIDDLE.getType(), RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front10, ColorUtils.colorFFFFFF, dealCard.getPromoStr());
    }


    public static DealDetailDto getDealStructDetail(DealProductDTO dealProductDTO) {
        if (dealProductDTO == null || CollectionUtils.isEmpty(dealProductDTO.getAttrs())) {
            return null;
        }
        Optional<DealProductAttrDTO> attrOpt = dealProductDTO.getAttrs().stream().filter(o-> Objects.equals(o.getName(), ConstantUtils.ATTR_STRUCT_DETAIL)).findFirst();
        if (attrOpt.isPresent()) {
            return JsonCodec.decode(attrOpt.get().getValue(), DealDetailDto.class);
        }
        return null;
    }

    //判断是否高优先级排序团单
    private boolean isPriorityOrderDeal(DealThemeDTO dealTheme, List<Integer> shopRecommendThemeIds, long navIdSelected, List<Integer> summaryDealIds) {
        try {
            //存在标签，当前2022.10 有头图标签的团单恰好是有高优级排序的团单
            FloatTagVO floatTagVO = buildFloatTag(dealTheme, shopRecommendThemeIds, navIdSelected);
            if (floatTagVO != null) {
                return true;
            }
            //为下挂团单，下挂团单都会高优排序
            return CollectionUtils.isNotEmpty(summaryDealIds) && summaryDealIds.contains(dealTheme.getDealId());
        }catch (Exception ex) {
            //ignore 打点而已，不影响主业务
            return false;
        }
    }

    @Data
    private static class ConstantConfig {
        /**
         * 玩乐卡icon
         */
        private String playCardIcon;

        /**
         * 玩乐卡icon宽高比
         */
        private double playCardIconAspectRadio;

        /**
         * 商家推荐图片
         */
        private String shopRecommendPic;

        /**
         * 商家推荐图片比例
         */
        private double shopRecommendAspectRadio;

        /**
         * 活动图片的比例
         */
        private double activityPicAspectRadio;
    }
}
