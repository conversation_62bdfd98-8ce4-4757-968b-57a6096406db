package com.sankuai.dzviewscene.productshelf.gateways.mapi;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.web.api.API;
import com.dianping.vc.web.api.URL;
import com.dianping.vc.web.biz.client.api.AppContext;
import com.dianping.vc.web.biz.client.api.AppContextAware;
import com.dianping.vc.web.biz.client.api.MTUserIdAware;
import com.dianping.vc.web.biz.client.api.UserIdAware;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceEngine;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceExecutionEngine;
import com.sankuai.dzviewscene.product.shelf.utils.GatewayUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.faulttolerance.ActivityContextRequestBuilder;
import com.sankuai.dzviewscene.shelf.faulttolerance.dealshelf.DealShelfProductFTConfiguration;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 只请求货架的商品部分，一般用于点击筛选Tab时的请求
 * @auther: liweilong06
 * @date: 2020/7/6 7:26 上午
 */
@URL(url = "/api/dzviewscene/productshelf/dzshelfdeals")
@Service
public class ShelfDealsApi implements API, AppContextAware, UserIdAware, MTUserIdAware {

    /**
     * 统一商户ID
     */
    @Setter
    private String shopuuid;

    /**
     * 定位的关键字
     */
    @Setter
    private String searchkeyword;

    /**
     * 下挂商品Id 【新】
     * Ex：{"deal":"1,2,3","spu":"4,5,6"}
     * deal - 团单， spu - 泛商品
     * 解析工具如下：
     * {@link ParamUtil#getSummarySpuIds(java.lang.String,java.lang.String)}
     */
    @Setter
    private String summarypids;

    /**
     * 用户选中/飘红的商品，需要强制的置顶
     */
    @Setter
    private String anchorgoodid;

    /**
     * 上游商品类型
     */
    @Setter
    private String biztype;

    /**
     * 商品ID列表，用","号分割，如：123,234
     */
    @Setter
    private String productids;


    /**
     * 扩展信息
     */
    @Setter
    private String extra;

    /**
     * 选择的导航ID
     */
    @Setter
    private long filterbtnid;

    /**
     * 筛选条件参数，用于多条件筛选，多个参数逗号分隔
     */
    @Setter
    private String filterparams;

    /**
     * 经度
     */
    @Setter
    private Double lng;

    /**
     * 纬度
     */
    @Setter
    private Double lat;

    /**
     * 经纬度坐标类型，货架默认为GCJ02
     */
    @Setter
    private String coordType = "GCJ02";

    /**
     * 城市Id
     */
    @Setter
    private Integer cityid;

    /**
     * 用户定位城市id
     */
    @Setter
    private Integer locationcityid;

    /**
     * 商户Id
     */
    @Setter
    private long shopid; // poiMigrate

    /**
     * 来自后端分配@float.lu
     */
    @Setter
    private String sceneCode;

    /**
     * 平台 {@link VCClientTypeEnum}
     * 100： dpapp
     * 101： m
     * 200： mtapp
     * 201： i
     */
    @Setter
    private int platform;

    /**
     * 客户端类型：ios | android | harmony | 空字符串
     */
    @Setter
    private String client= "";

    /**
     * 版本号
     */
    @Setter
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    private String deviceId;

    /**
     * unionid
     */
    @Setter
    private String unionId;

    /**
     * 点评用户ID
     */
    private long dpUserId;

    /**
     * 美团用户ID
     */
    private long mtUserId;

    /**
     * MTSI反爬标识，其值来源于请求头，直接透传
     */
    @Setter
    private String mtsiflag;

    /**
     * 货架模块版本，由前端维护·
     */
    @Setter
    private int shelfversion;

    /**
     * 页面来源标记，用于在跳转链接上加来源标识后缀,前端直接传"mtlm=xxx"这种key+value样式的信息 需让前端先encode，后端会自动解析
     */
    @Setter
    private String pagesource;

    /**
     * 猜喜侧传入商品，可能是到综商品、到餐商品
     */
    @Setter
    private String recommendinfo;

    /**
     * 价格一致率透传加密字符串
     */
    @Setter
    private String pricecipher;

    /**
     * 神会员点位，废弃，后端自动识别
     * com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil#getPosition(int, java.lang.String)
     */
    @Deprecated
    @Setter
    private String position;

    @Setter
    private Integer pageindex;

    @Setter
    private Integer pagesize;

    /**
     * 刷新标记
     */
    @Setter
    private String refreshtag;

    /**
     * regionId，到家set化用
     */
    @Setter
    private String wtt_region_id;

    @Setter
    private String pagination;

    /**
     * 屏幕高度，用户提前获取团详页布局信息，做闪开方案
     */
    @Setter
    private Double deviceHeight;

    @Setter
    private String appid;

    @Setter
    private String channelType;

    @Resource
    private ActivityContextRequestBuilder activityContextRequestBuilder;

    private FaultToleranceEngine faultToleranceEngine = new FaultToleranceExecutionEngine();

    @Resource
    private DealShelfProductFTConfiguration dealShelfProductFTConfiguration;

    @Override
    public Object execute() {
        GatewayUtils.ignoreNullField(platform);
        ActivityContextRequest request = activityContextRequestBuilder.buildActivityContextRequest(getActivityContextRequest());
        return faultToleranceEngine.execute(request, dealShelfProductFTConfiguration);
    }

    private ActivityContextRequest getActivityContextRequest() {
        ActivityContextRequest request = new ActivityContextRequest();
        request.setSceneCode(sceneCode);
        request.setSearchKeyword(searchkeyword);
        request.setFilterBtnId(filterbtnid);
        request.setFilterParams(filterparams);
        request.setShopUuid(shopuuid);
        request.setPlatform(platform);
        request.setUnionId(unionId);
        request.setDeviceId(deviceId);
        request.setClient(client);
        request.setVersion(version);
        request.setChannel(ShelfActivityConstants.ChannelType.dealShelfListForTab);
        request.setShopId(shopid);
        request.setCityId(cityid);
        request.setLocationCityId(locationcityid);
        request.setUserId(getUserId());
        request.setTopProductIds(productids);
        request.setSummaryProductIds(summarypids);
        request.setAnchorGoodId(anchorgoodid);
        request.setBizType(biztype);
        request.setLng(lng);
        request.setLat(lat);
        request.setCoordType(coordType);
        request.setExtra(extra);
        request.setShelfVersion(shelfversion);
        request.setMtSIFlag(mtsiflag);
        request.setPageSource(pagesource);
        request.setRecommendinfo(recommendinfo);
        request.setPricecipher(pricecipher);
        request.setPosition(position);
        request.setRefreshTag(refreshtag);
        request.setWttRegionId(wtt_region_id);
        request.setPageindex(Optional.ofNullable(pageindex).orElse(0));
        request.setPagesize(Optional.ofNullable(pagesize).orElse(0));
        request.setPagination(pagination);
        if (deviceHeight != null) {
            request.setDeviceHeight((int) Math.round(deviceHeight));
        }
        request.setAppId(appid);
        request.setOpenId(VCClientTypeEnum.MT_XCX.getCode() == platform ? unionId : null);
        request.setChannelType(channelType);
        return request;
    }

    @Override
    public void setAppContext(AppContext appContext) {
        deviceId = appContext.getDpid();
        client = getClient(appContext.getClient());
        version = getVersion(appContext.getVersion());
        unionId = getUnionId(appContext.getUnionid());
        platform = getPlatform(appContext.getPlatform());
        mtsiflag = getMtSIFlag(appContext.getMtsiflag());
    }


    private String getVersion(String currentVersion) {
        if (StringUtils.isNotEmpty(version)) {
            return version;
        }
        return currentVersion;
    }

    private String getClient(String currentClient) {
        if (StringUtils.isEmpty(client)) return currentClient;
        return client;
    }

    private String getMtSIFlag(String mtSIFlag) {
        if (StringUtils.isNotEmpty(mtsiflag)) {
            return mtsiflag;
        }
        return mtSIFlag;
    }

    private String getUnionId(String currentUnionId) {
        if (StringUtils.isEmpty(unionId)) return currentUnionId;
        return unionId;
    }

    private int getPlatform(String currentPlatform) {
        if (platform > 0) {
            return platform;
        }
        if (StringUtils.isEmpty(currentPlatform) || currentPlatform.equals("dp")) {
            return VCClientTypeEnum.DP_APP.getCode();
        }
        return VCClientTypeEnum.MT_APP.getCode();
    }

    @Override
    public void setMTUserId(long mtUserId) {
        this.mtUserId = mtUserId;
    }

    @Override
    public void setUserId(long dpUserId) {
        this.dpUserId = dpUserId;
    }

    private long getUserId() {
        if (PlatformUtil.isMT(platform)) {
            return mtUserId;
        }
        return dpUserId;
    }

}
