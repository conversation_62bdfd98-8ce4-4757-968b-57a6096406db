package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.dianping.cat.Cat;
import com.dianping.mobile.base.datatypes.utils.VersionUtil;
import org.apache.commons.lang.StringUtils;


public class VersionUtils {

    public static final String MT_YELLOW_VERSION = "10.2.200";
    public static final String DP_VERSION_10_17_0 = "10.17.0";

    //美团泛拼场接入剧本杀版本
    public static final String DP_VERSION_10_46_0 = "10.46.0";


    /**
     * 足疗货架充值套餐模块
     * 起始版本为DP10.17.0 ，MT10.2.200
     *
     * @param isMt
     * @param version
     * @return
     */
    public static boolean isUpVersionMt_10_2_200OrDp10_17_0(boolean isMt, String version) {
        if (StringUtils.isEmpty(version)) {
            return false;
        }
        if (isMt) {
            return VersionUtil.compare(version, MT_YELLOW_VERSION) >= 0;
        }
        return VersionUtil.compare(version, DP_VERSION_10_17_0) >= 0;
    }

    /**
     * 泛拼场接入剧本杀版本，起始版本DP 10.46.0, 美团侧无需版控
     * @param isMt
     * @param version
     * @return
     */
    public static boolean isUpVersionDp10_46_0(boolean isMt, String version) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.productshelf.vu.biz.utils.VersionUtils.isUpVersionDp10_46_0(boolean,java.lang.String)");
        if (isMt) {
            return true;
        }
        return VersionUtil.compare(version, DP_VERSION_10_46_0) >= 0;
    }

}
