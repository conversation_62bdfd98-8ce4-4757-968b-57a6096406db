package com.sankuai.dzviewscene.product.shelf.options.builder.activity;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoDisplaySourceTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.activity.vp.ActivityEndTimeVP;
import com.sankuai.dzviewscene.product.shelf.utils.ShelfPromoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2023-06-05-16:06
 */
@VPointOption(name = "整个货架-当日最晚活动结束时间",
        description = "取单组商品立减的结束时间计算",
        code = "ActivitiesLatestEndTimeOpt")
public class ActivitiesLatestEndTimeOpt extends ActivityEndTimeVP<ActivitiesLatestEndTimeOpt.Config> {

    @Override
    public Long compute(ActivityCxt context, ActivityEndTimeVP.Param param, ActivitiesLatestEndTimeOpt.Config config) {
        ShelfGroupM shelfGroupM = param.getShelfGroupM();
        if (Objects.isNull(shelfGroupM) || MapUtils.isEmpty(shelfGroupM.getProductGroupMs())) {
            return 0L;
        }
        //目前仅用于堆头货架，不考虑多组情况
        List<ProductM> products = shelfGroupM.getProductGroupMs().values().stream()
                .filter(productGroupM -> productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts()))
                .map(ProductGroupM::getProducts).findFirst().orElse(null);
        return buildLatestEndTime(products, config.enablePeriodReduction);
    }

    private long buildLatestEndTime(List<ProductM> products, boolean enablePeriodReduction) {
        if (CollectionUtils.isEmpty(products)) {
            return 0;
        }
        List<DateTime> promoEndTimeList = products.stream()
                .map(product -> ShelfPromoUtils.getShopDirectPromoEndTime(product.getPromoPrices(), enablePeriodReduction))
                .filter(Objects::nonNull).collect(Collectors.toList());
        DateTime latestEndTime = promoEndTimeList.stream().filter(time -> time.isAfter(DateTime.now())).max(DateTime::compareTo).orElse(null);
        if (Objects.isNull(latestEndTime)) {
            return 0;
        }
        DateTime endOfToday = DateTime.now().withTimeAtStartOfDay().plusDays(1);
        DateTime latestActivityEndTime = new DateTime(latestEndTime);
        return latestActivityEndTime.isAfter(endOfToday) ? endOfToday.getMillis() : latestActivityEndTime.getMillis();
    }

    @VPointCfg
    @Data
    public static class Config {
        /*
        是否开启分时段立减的倒计时计算，用于长期立减的每日立减结束时间计算
         */
        private boolean enablePeriodReduction = false;
    }
}
