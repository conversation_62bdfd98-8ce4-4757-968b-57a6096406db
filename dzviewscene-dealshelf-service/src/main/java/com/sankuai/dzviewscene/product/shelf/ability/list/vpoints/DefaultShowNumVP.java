package com.sankuai.dzviewscene.product.shelf.ability.list.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by float on 2021/8/21.
 */
@VPoint(name = "货架展开商品个数", description = "货架展开商品个数",code = DefaultShowNumVP.CODE, ability = ShelfProductListBuilder.CODE)
public abstract class DefaultShowNumVP<T> extends PmfVPoint<Integer, DefaultShowNumVP.Param, T> {

    public static final String CODE = "DefaultShowNumVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> productMs;
    }
}
