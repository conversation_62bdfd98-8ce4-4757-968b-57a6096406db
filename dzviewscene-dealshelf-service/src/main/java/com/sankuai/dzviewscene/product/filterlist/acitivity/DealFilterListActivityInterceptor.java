package com.sankuai.dzviewscene.product.filterlist.acitivity;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.annotation.ActivityInterceptor;
import com.sankuai.athena.viewscene.framework.core.IActivityInterceptor;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;


@ActivityInterceptor(name = "团单筛选列表活动打点拦截器", code = "deal_filter_list_activity_interceptor", activity = DealFilterListActivity.CODE)
public class DealFilterListActivityInterceptor implements IActivityInterceptor<DzFilterProductListVO> {

    @Override
    public void beforeExecute(InterceptorContext<DzFilterProductListVO> interceptorContext) {

    }

    @Override
    public void complete(InterceptorContext<DzFilterProductListVO> interceptorContext, DzFilterProductListVO result) {
        try {
            Cat.logMetricForCount(interceptorContext.getActivityCode(), buildMetricTags(interceptorContext, result));
        } catch (Exception e) {/*静默*/}
    }

    private Map<String, String> buildMetricTags(InterceptorContext<DzFilterProductListVO> interceptorContext, DzFilterProductListVO dzShelfResponseVO) {
        DzFilterProductListVO finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : dzShelfResponseVO;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("hasFilters", Boolean.toString(ModelUtils.hasFilters(finalResult)));
            put("hasProducts", Boolean.toString(ModelUtils.hasProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if(StringUtils.isNotEmpty(clientTypeMsg)){
                put("clientType", clientTypeMsg);
            }
        }};
    }
}
