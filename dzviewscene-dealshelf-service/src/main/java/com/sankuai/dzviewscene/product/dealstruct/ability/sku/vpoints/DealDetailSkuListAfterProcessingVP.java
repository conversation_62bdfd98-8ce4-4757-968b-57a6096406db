package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/16 9:39 上午
 */
@VPoint(name = "团购详情sku货列表后置处理", description = "团购详情sku货列表后置处理",code = DealDetailSkuListAfterProcessingVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class DealDetailSkuListAfterProcessingVP<T> extends PmfVPoint<List<DealSkuVO>, DealDetailSkuListAfterProcessingVP.Param, T> {

    public static final String CODE = "DealDetailSkuListAfterProcessingVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private boolean isMustGroup;
        private List<DealSkuVO> dealSkuVOS;
        private List<SkuItemDto> skuItems;
        private List<ProductSkuCategoryModel> productCategories;
    }
}
