package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BathSkuUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @author: created by hang.yu on 2023/10/18 10:27
 */
@VPointOption(name = "洗浴sku属性列表默认变化点", description = "洗浴sku属性列表默认变化点，展示配置的sku属性", code = BathSkuAttrListOpt.CODE, isDefault = true)
public class BathSkuAttrListOpt extends SkuAttrListVP<BathSkuAttrListOpt.Config> {

    public static final String CODE = "BathSkuAttrListOpt";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        // 获取
        String remark = BathSkuUtils.buildSkuInfo(param.getSkuItemDto());
        if (StringUtils.isBlank(remark)) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setValue(remark);
        return Lists.newArrayList(dealSkuItemVO);
    }


    @Data
    @VPointCfg
    public static class Config {

    }
}
