package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemdisplaysaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemDisplaySalePriceVP;

@VPointOption(name = "默认展示到手价",
        description = "默认展示到手价",
        code = DefaultItemDisplaySalePriceOpt.CODE,
        isDefault = true)
public class DefaultItemDisplaySalePriceOpt extends UnifiedShelfItemDisplaySalePriceVP<Void> {

    public static final String CODE = "DefaultItemDisplaySalePriceOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return param.getSalePrice();
    }
}