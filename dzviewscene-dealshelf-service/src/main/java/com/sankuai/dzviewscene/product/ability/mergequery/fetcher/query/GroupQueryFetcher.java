package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.exception.BusinessException;
import com.sankuai.dzviewscene.product.ability.mergequery.MergeQueryCfg;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.*;
import com.sankuai.dzviewscene.product.utils.CfUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
@SuppressWarnings("all")
public class GroupQueryFetcher extends QueryConstants implements BeanFactoryAware {

    private static Map<String, Class<? extends QueryHandler>> queryType2Handlers = new HashMap<>();

    static {
        //////////////////////////////////召回渠道池//////////////////////////////////////
        queryType2Handlers.put(QueryType.targetIdQuery.name(), TargetProductIdQueryHandler.class);
        queryType2Handlers.put(QueryType.citySpuProductsQueryHandler.name(), CitySpuProductsQueryHandler.class);
        queryType2Handlers.put(QueryType.poiSpuProductsQueryHandler.name(), PoiSpuProductsQueryHandler.class);
        queryType2Handlers.put(QueryType.dpCollectProductsQueryHandler.name(), CollectProductsQueryHandler.class);

        queryType2Handlers.put(QueryType.poiSaleDealGroupQueryHandler.name(), PoiSaleDealGroupQueryHandler.class);
        queryType2Handlers.put(QueryType.poiTimesCardQueryHandler.name(), PoiTimesCardQueryHandler.class);
        queryType2Handlers.put(QueryType.poiPackProductQueryHandler.name(), PoiPackProductQueryHandler.class);

    }

    private BeanFactory beanFactory;

    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt ctx, MergeQueryCfg config) {
        // 1. 获取要召回的商品组列表
        if (CollectionUtils.isEmpty(config.getGroupNames())) {
            throw new BusinessException("groupNames参数不能为空");
        }

        // 2. 获取要召回的商品组参数
        if (MapUtils.isEmpty(config.getGroupParams())) {
            throw new BusinessException("groupParams参数不能为空");
        }

        // 3. 批量召回
        Map<String, CompletableFuture<ProductGroupM>> productGroupCompletableMap = batchGetProductGroup(ctx, config.getGroupNames(), config.getGroupParams());

        if (MapUtils.isEmpty(productGroupCompletableMap)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 4. 结构转换
        return CfUtils.each(productGroupCompletableMap);
    }

    // 查询一组商品
    private Map<String, CompletableFuture<ProductGroupM>> batchGetProductGroup(ActivityCxt ctx, List<String> groupNames, Map<String, Map<String, Object>> multiGroupParams) {
        if (CollectionUtils.isEmpty(groupNames)) {
            return Maps.newHashMap();
        }
        return groupNames.stream()
                .collect(HashMap::new,
                        (map, groupName) -> {
                            map.put(groupName, querySingleProductGroup(ctx, groupName, multiGroupParams.get(groupName)));
                        },
                        HashMap::putAll);
    }

    private CompletableFuture<ProductGroupM> querySingleProductGroup(ActivityCxt ctx, String groupName, Map<String, Object> groupParams) {
        if (MapUtils.isEmpty(groupParams)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }

        String queryType = ParamsUtil.getStringSafely(groupParams, QueryConstants.Params.queryType);

        Class queryHandlerClazz = queryType2Handlers.get(queryType);
        if (queryHandlerClazz == null) {
            throw new BusinessException(String.format("查找召回器=%s, 找不到", queryType));
        }

        QueryHandler groupQueryHandler = (QueryHandler) beanFactory.getBean(queryHandlerClazz);
        if (groupQueryHandler == null) {
            throw new BusinessException(String.format("查找召回器=%s, 找不到", queryHandlerClazz.getName()));
        }

        return groupQueryHandler.query(ctx, groupName, groupParams);
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}