package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @description :
 * @date : 2025/4/22
 */
@VPoint(name = "优惠感知相关", description = "优惠感知相关", code = ProductPriceBottomTagsVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPriceBottomTagsVP<T> extends PmfVPoint<List<DzTagVO>, ProductPriceBottomTagsVP.Param, T> {
    public static final String CODE = "ProductPriceBottomTagsVP";


    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private List<DzPromoVO> dzPromoVOS;
        private CardM cardM;
    }
}
