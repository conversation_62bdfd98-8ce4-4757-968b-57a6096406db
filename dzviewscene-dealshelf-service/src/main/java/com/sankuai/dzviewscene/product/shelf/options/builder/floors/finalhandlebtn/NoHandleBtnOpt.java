package com.sankuai.dzviewscene.product.shelf.options.builder.floors.finalhandlebtn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FinalHandleBtnVP;
import lombok.Data;

@VPointOption(name = "通用商品购买信息，通过配置来启动某些通用逻辑",
        description = "",
        code = "NoHandleBtnOpt",
        isDefault = true)
public class NoHandleBtnOpt extends FinalHandleBtnVP<NoHandleBtnOpt.Config> {

    @Override
    public Void compute(ActivityCxt activityCxt, Param param, Config config) {
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}