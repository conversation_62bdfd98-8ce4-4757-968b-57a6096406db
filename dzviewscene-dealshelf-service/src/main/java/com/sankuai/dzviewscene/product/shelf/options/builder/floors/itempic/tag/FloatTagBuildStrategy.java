package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag;

import com.sankuai.dzviewscene.dealshelf.shelfvo.FloatTagModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicFloatTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import scala.annotation.meta.param;

/**
 * <AUTHOR>
 * @date 2023/3/28
 */
public interface FloatTagBuildStrategy {
    /**
     * @param param
     * @param config 非强制，多数情况下可为 null
     * @return 单个构造标签
     */
    FloatTagVO build(FloatTagBuildReq param, FloatTagBuildCfg config);

    /**
     * 唯一的策略名
     * @return
     */
    String getName();


    /**
     * 新版货架模型构造
     * @param param
     * @param config
     * @return
     */
    FloatTagModel newBuild(FloatTagBuildReq param, FloatTagBuildCfg config);
}
