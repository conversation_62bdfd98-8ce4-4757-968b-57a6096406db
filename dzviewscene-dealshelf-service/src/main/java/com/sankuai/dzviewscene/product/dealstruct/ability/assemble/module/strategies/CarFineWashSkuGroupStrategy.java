package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * created by leimengdan in 2022/11/02
 */
@Component("carFineWashSkuGroupStrategy")
public class CarFineWashSkuGroupStrategy implements ModuleStrategy {

    /**
     * 养车用车-精洗 场景
     */
    private final String CAR_FINE_WASH_CAR_DEAL_DETAIL = "car_fine_wash_car_deal_detail";
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.car.wash.strategy.content", defaultValue = "{}")
    private Config lionConfig;

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        //1、获取需要的依赖数据
        List<SkuAttrItemDto> skuAttrItemDtos = getSkuAttrList(activityCxt);
        //2、组装VOList
        List<DealSkuVO> dealSkuVOList = buildDealSkuVOList(activityCxt.getSceneCode(), skuAttrItemDtos, config);
        //3、组装为结果VO
        return buildDealDetailModuleVO(dealSkuVOList);
    }

    private List<SkuAttrItemDto> getSkuAttrList(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (dealDetailInfoModel == null) {
            return null;
        }
        return DealDetailUtils.getFirstMustGroupFirstSkuAttrList(dealDetailInfoModel.getDealDetailDtoModel());
    }

    /**
     * 构造sku列表组
     *@param
     *@return
     */
    private List<DealSkuVO> buildDealSkuVOList(String sceneCode, List<SkuAttrItemDto> skuAttrItemDtos, String groupName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || isShowStandardDealComparePic(sceneCode, skuAttrItemDtos)) {
            return null;
        }
        Map<String, String> fineWashSkuAttrName2IconMap = getFineWashSkuAttrName2IconMap(groupName);
        if (MapUtils.isEmpty(fineWashSkuAttrName2IconMap)) {
            return null;
        }
        return fineWashSkuAttrName2IconMap.entrySet().stream()
                .map(entry -> {
                    String title = getSkuTitle(skuAttrItemDtos, entry.getKey());
                    return buildDealSkuVO(entry.getValue(), title);
                }).filter(vo -> vo != null)
                .collect(Collectors.toList());
    }

    /**
     * 如果团单服务项目属性facadeServiceAction值为"车身预洗流挂，泡沫冲洗、漆面水蜡上光、车身边缝清洗收水、轮毂刷洗、轮胎上釉、发动机舱表面除尘"
     * 或"车身预洗流挂，泡沫冲洗、漆面喷洒专用去除剂，静置溶解后冲洗，去除铁粉/柏油/鸟屎/树胶、漆面水蜡上光、车身边缝清洗收水、轮毂刷洗、轮胎上釉、发动机舱表面除尘"时，
     * 不展示内饰清洁和外观清洁服务项目模块，改为展示对比图
     *@param
     *@return
     */
    private boolean isShowStandardDealComparePic(String sceneCode, List<SkuAttrItemDto> skuAttrItemDtos) {
        if (lionConfig == null || CollectionUtils.isEmpty(lionConfig.getShowComparePicServiceActions())) {
            //如果没有配置，则都不展示
            return false;
        }
        String facadeServiceAction = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "facadeServiceAction");
        // 养车用车-精洗团详 没有对比图
        if (StringUtils.isNotBlank(sceneCode) && CAR_FINE_WASH_CAR_DEAL_DETAIL.equals(sceneCode)){
            return false;
        }
        return lionConfig.getShowComparePicServiceActions().contains(facadeServiceAction);
    }

    /**
     * 获取sku名称
     *@param
     *@return
     */
    private String getSkuTitle(List<SkuAttrItemDto> skuAttrItemDtos, String attrName) {
        String attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, attrName);
        String attrChnName = DealDetailUtils.getSkuAttrChnNameBySkuAttrName(skuAttrItemDtos, attrName);
        if (StringUtils.isEmpty(attrValue) || StringUtils.isEmpty(attrChnName)) {
            return null;
        }
        return String.format("%s：%s", attrChnName, attrValue);
    }


    /**
     * 按照组名获取该组下应该展示的sku属性名和对应的icon
     *@param
     *@return sku属性名到该属性icon Map
     */
    private Map<String, String> getFineWashSkuAttrName2IconMap(String groupName) {
        if (lionConfig == null || CollectionUtils.isEmpty(lionConfig.getFineWashSkuGroupAttrConfigs()) || StringUtils.isEmpty(groupName)) {
            return new HashMap<>();
        }
        FineWashSkuGroupAttrConfig fineWashSkuGroupAttrConfig = lionConfig.getFineWashSkuGroupAttrConfigs().stream().filter(config -> Objects.equals(config.getGroupName(), groupName)).findFirst().orElse(null);
        return fineWashSkuGroupAttrConfig == null ?  new HashMap<>() : fineWashSkuGroupAttrConfig.getFineWashSkuAttrName2IconMap();
    }

    private DealSkuVO buildDealSkuVO(String icon, String title) {
        if (StringUtils.isEmpty(title)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setIcon(icon);
        return dealSkuVO;
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<DealSkuVO> dealSkuVOList) {
        if (CollectionUtils.isEmpty(dealSkuVOList)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        dealDetailModuleVO.setSkuGroupsModel1(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailModuleVO;
    }

    @Data
    public static class Config {
        private List<String> productCategoryList;
        private String defaultIcon;
        private Map<String, List<AttrVO>> categoryId2VO;
        private String title;
        //白名单内的团单不展示"整车外表清洁"和"内饰清洁除尘"下的属性信息
        private List<Integer> dpDealIdWhiteList;
        private List<Integer> mtDealIdWhiteList;
        private List<String> skuTitleWithoutSkuAttrInfo;
        //精洗团单sku名对应展示的sku属性名Map
        private List<FineWashSkuGroupAttrConfig> fineWashSkuGroupAttrConfigs;
        /**
         * 展示对比图的 facadeServiceAction 的value
         */
        private List<String> showComparePicServiceActions;
    }

    @Data
    public static class FineWashSkuGroupAttrConfig {
        //精洗sku组名
        private String groupName;
        //精洗ku属性名到属性icon对应map
        private Map<String, String> fineWashSkuAttrName2IconMap;
    }

    @Data
    public static class AttrVO {
        private String name;
        private String icon;
        private String desc;
    }
}
