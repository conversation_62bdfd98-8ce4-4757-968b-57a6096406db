package com.sankuai.dzviewscene.product.unifiedshelf.enums;

public enum RichLabelStyleEnum {
    // 通用
    DEFAULT(0,"默认"),

    ROUND_CORNER(1,"圆角"),

    BUBBLE(2, "气泡样式"),

    // 业务定制
    DP_MAGICAL_MEMBER(101, "点评神会员样式"),
    MT_MAGICAL_MEMBER(102, "美团神会员样式"),
    MAGICAL_MEMBER_GRADIENT_STYLE(103, "美团神会员优惠标签规范样式(渐变)"),
    MAGICAL_MEMBER_NORMAL_STYLE(104, "点评神会员优惠标签规范样式(浅色)")
    ;

    private int type;

    private String desc;

    RichLabelStyleEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }
}
