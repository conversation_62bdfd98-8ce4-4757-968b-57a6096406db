package com.sankuai.dzviewscene.product.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DzItemVO 的公共样式构造工具
 * <AUTHOR>
 * @date 2022/3/30
 */
public class DzItemVOStyleUtils {

    private static final String GREY_COLOR = "#777777";

    /**
     * 获取商家推荐的角标
     * 若团单非商家推荐单，则 return null
     * @param productM
     * @return
     */
    public static FloatTagVO getShopRecommendFloatTag(ProductM productM) {
        if (!ProductMAttrUtils.isShopRecommend(productM)) {
            return null;
        }
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl("https://p1.meituan.net/dprainbow/6b8ad53cc2d030a93b85d0c1d34bc8bd8626.png");
        floatTagPic.setAspectRadio(2.77778);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_TOP.getPosition());
        return floatTagVO;
    }

    /**
     * @param tags
     * @param color 未指定颜色时会使用默认的灰色
     * @return
     */
    public static List<RichLabelVO> batchConvertTextTag2RichLabelProductTags(List<String> tags, String color) {
        if (CollectionUtils.isEmpty(tags)) {
            return new ArrayList<>();
        }
        String selectColor = StringUtils.isEmpty(color) ? GREY_COLOR : color;
        return tags.stream().filter(StringUtils::isNotEmpty).map(tag -> DzItemVOStyleUtils.convertTag2TargetColorRichLabelProductTag(tag, selectColor)).collect(Collectors.toList());
    }

    /**
     * @param tag
     * @return 副标题富文本-灰色
     */
    public static RichLabelVO convertTag2GrayRichLabelProductTag(String tag) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.utils.DzItemVOStyleUtils.convertTag2GrayRichLabelProductTag(java.lang.String)");
        return convertTag2TargetColorRichLabelProductTag(tag, GREY_COLOR);
    }

    /**
     * @param tag
     * @return 副标题富文本-黑色
     */
    public static RichLabelVO convertTag2BlackRichLabelProductTag(String tag) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.utils.DzItemVOStyleUtils.convertTag2BlackRichLabelProductTag(java.lang.String)");
        return convertTag2TargetColorRichLabelProductTag(tag, "#111111");
    }

    /**
     * @param tag
     * @param color
     * @return 转换成指定颜色副标题
     */
    private static RichLabelVO convertTag2TargetColorRichLabelProductTag(String tag, String color) {
        return new RichLabelVO("normal", 11, color, tag);
    }

    /**
     * @param url
     * @param aspectRadio 宽高比
     * @return 构造左下角图片标签（通常为营销位）
     */
    public static FloatTagVO buildLeftBottomPicTag(String url, double aspectRadio) {
        if (StringUtils.isEmpty(url) || aspectRadio <= 0) {
            return null;
        }
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl(url);
        floatTagPic.setAspectRadio(aspectRadio);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        return floatTagVO;
    }
}
