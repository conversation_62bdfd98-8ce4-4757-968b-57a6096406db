package com.sankuai.dzviewscene.product.unifiedshelf.enums;

public enum FilterNodeShowTypeEnum {
    FILTER_NORMAL_TAG(100, "带背景色样式筛选"),

    FILTER_PLAIN_TAG(101,"不带背景色样式筛选"),

    FILTER_ADAPTIVE_GRID_LAYER(102, "自适应网格浮层筛选"),

    FILTER_LIST_LAYER(103, "列表浮层筛选"),

    FILTER_GRID_LAYER(104, "网格浮层筛选，有重制/查看交互"),

    FILTER_GROUP_GRID_LAYER(105, "分组网格浮层筛选，有重制/查看交互"),

    FILTER_F_SLIDE_LAYER(106, "F型浮层筛选，分类导航滑动"),

    FILTER_F_SWITCH_LAYER(107, "F型浮层筛选，分类导航切换");

    private int type;
    private String desc;

    private FilterNodeShowTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return this.type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return this.desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static FilterNodeShowTypeEnum from(int type) {
        for (FilterNodeShowTypeEnum value : FilterNodeShowTypeEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }
}
