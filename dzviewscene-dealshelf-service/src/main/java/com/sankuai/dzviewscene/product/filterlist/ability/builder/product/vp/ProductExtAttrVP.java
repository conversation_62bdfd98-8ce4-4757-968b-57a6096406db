package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/24
 */
@VPoint(name = "商品-扩展信息", description = "商品-attrs，可放埋点信息", code = ProductExtAttrVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductExtAttrVP<T> extends PmfVPoint<String, ProductExtAttrVP.Param, T> {
    public static final String CODE = "ProductExtAttrVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductM productM;

    }

}
