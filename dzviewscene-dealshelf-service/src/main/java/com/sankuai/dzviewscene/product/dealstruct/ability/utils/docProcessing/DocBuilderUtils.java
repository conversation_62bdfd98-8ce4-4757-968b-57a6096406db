package com.sankuai.dzviewscene.product.dealstruct.ability.utils.docProcessing;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.monitor.TraceElement;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/8 8:34 下午
 */
public class DocBuilderUtils {

    static {
        //添加自定义函数
    }

    /**
     * 判断对应规则下构造的文案是否需要展示
     *@param verifyExpressionDoc 校验表达式
     *@param dataResource 校验所需的数据源
     *@return 是否展示
     */
    public static boolean verify(String verifyExpressionDoc, Map<String, Object> dataResource, ActivityCxt context) {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(verifyExpressionDoc) || MapUtils.isEmpty(dataResource)) {
            return false;
        }
        try {
            Expression expression = AviatorEvaluator.getInstance().compile(verifyExpressionDoc, true);
            return Boolean.TRUE.toString().equals(expression.execute(dataResource).toString());
        } catch (Exception e) {
            Cat.logErrorWithCategory(String.format("DocBuilderUtils#verify#verifyExpressionDoc:%s", verifyExpressionDoc), e);
            context.addTrace(TraceElement.build(
                    "业务点",
                    "文案展示校验表达式",
                    String.format("verifyExpressionDoc:%s;dataResource:%s", verifyExpressionDoc, JsonCodec.encodeWithUTF8(dataResource)),
                    null,
                    e,
                    System.currentTimeMillis() - startTime
            ));
            return false;
        }
    }

    /**
     * 构造最终用于展示的文案
     *@param buildExpressionDoc 构造表达式
     *@param dataResource 构造文案所需的数据源，例如服务项目属性，团单属性
     *@return 展示的文案
     */
    public static Object build(String buildExpressionDoc, Map<String, Object> dataResource, ActivityCxt context) {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isEmpty(buildExpressionDoc) || MapUtils.isEmpty(dataResource)) {
            return null;
        }
        try {
            Expression expression = AviatorEvaluator.getInstance().compile(buildExpressionDoc, true);
            return expression.execute(dataResource);
        } catch (Exception e) {
            Cat.logErrorWithCategory(String.format("DocBuilderUtils#build#buildExpressionDoc:%s", buildExpressionDoc), e);
            context.addTrace(TraceElement.build(
                    "业务点",
                    "文案展示构造表达式",
                    String.format("buildExpressionDoc:%s;dataResource:%s", buildExpressionDoc, JsonCodec.encodeWithUTF8(dataResource)),
                    null,
                    e,
                    System.currentTimeMillis() - startTime
            ));
            return null;
        }
    }

    /**
     * 根据脚本表达式构造String结果值
     *@param
     *@return
     */
    public static String getStringValueByScriptExpression(Map<String, Object> resource, List<DocBuilderUtils.BuildModel> buildModels, ActivityCxt context) {
        Object obj = DocBuilderUtils.getValueByScriptExpression(resource, buildModels, context);
        return obj == null ? null : obj.toString();
    }

    /**
     * 根据脚本表达式构造结果值
     *@param
     *@return
     */
    public static Object getValueByScriptExpression(Map<String, Object> resource, List<BuildModel> buildModels, ActivityCxt context) {
        if (CollectionUtils.isEmpty(buildModels)) {
            return null;
        }
        for (DocBuilderUtils.BuildModel buildModel : buildModels) {
            //通过配置的属性值展示校验语句判断该属性值是否需要展示
            if (DocBuilderUtils.verify(buildModel.getVerifyExpression(), resource, context)) {
                //通过配置的属性展示构造语句构造属性值文案
                return DocBuilderUtils.build(buildModel.getBuildExpression(), resource, context);
            }
        }
        return null;
    }

    @Data
    public static class BuildModel {
        //用于判断该值是否应该展示的校验表达式
        private String verifyExpression;
        //用于构造该值最终展示结果的构造表达式
        private String buildExpression;
    }

}
