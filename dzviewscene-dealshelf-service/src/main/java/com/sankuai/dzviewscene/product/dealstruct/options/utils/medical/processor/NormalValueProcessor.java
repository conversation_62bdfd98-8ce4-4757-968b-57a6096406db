package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

public class NormalValueProcessor extends AbstractValueProcessor {

    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        if (validate(value, valueConfig)) {
            return Lists.newArrayList();
        }
        if (Boolean.TRUE.equals(valueConfig.getPriceProcess())) {
            value = priceProcess(value);
        }
        if (!ObjectUtils.isEmpty(valueConfig.getFormat())) {
            value = String.format(valueConfig.getFormat(), value);
        }
        if (ObjectUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(value);
    }

    public static String priceProcess(String value) {
        if (value.endsWith("元")) {
            value = value.substring(0, value.length() - 1);
        }
        if (value.startsWith("¥")) {
            value = value.substring(1);
        }
        String reslut = value;
        if (value.contains("-")) {
            String[] split = value.split("-");
            if (isNumeric(split[0]) && isNumeric(split[1])) {
                reslut = "¥" + pricePointProcess(split[0]) + "-¥" + pricePointProcess(split[1]);
            }
        } else if (isNumeric(value)){
            reslut =  "¥" + pricePointProcess(value);
        }
        return reslut;
    }

    private static String pricePointProcess(String value) {
        String reslut = value;
        BigDecimal bigDecimal = new BigDecimal(reslut);
        bigDecimal = bigDecimal.setScale(2, RoundingMode.HALF_UP);
        if (bigDecimal.stripTrailingZeros().scale() <= 0) {
            reslut = bigDecimal.toBigInteger().toString();
        } else {
            reslut = bigDecimal.toString().replaceAll("0*$", "");
        }

        return reslut;
    }

    private static boolean isNumeric(String str) {
        return str != null && str.matches("^(\\d+\\.{0,1}\\d*)$");
    }

    private boolean validate(String value, ValueConfig valueConfig) {
        return ObjectUtils.isEmpty(value) || ObjectUtils.isEmpty(valueConfig);
    }
}
