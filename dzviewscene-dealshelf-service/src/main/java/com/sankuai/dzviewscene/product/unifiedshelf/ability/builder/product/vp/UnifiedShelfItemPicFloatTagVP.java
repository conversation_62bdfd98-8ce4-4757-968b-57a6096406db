/*
 * Create Author  : liyanmin
 * Create Date    : 2024-09-12
 * Project        :
 * File Name      : UnifiedShelfItemPicVP.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.FloatTagModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-12
 * @since dzviewscene-dealshelf-home 1.0
 */
@VPoint(name = "商品图片角标",
        description = "商品-图片角标",
        code = UnifiedShelfItemPicFloatTagVP.CODE,
        ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemPicFloatTagVP<T> extends PmfVPoint<List<FloatTagModel>, UnifiedShelfItemPicFloatTagVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemPicFloatTagVP";
    

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private long filterId;

        private int platform;

        private int index;
    }
}
