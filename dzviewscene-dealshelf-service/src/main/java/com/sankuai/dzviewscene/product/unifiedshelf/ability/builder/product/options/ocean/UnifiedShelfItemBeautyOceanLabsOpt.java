package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Objects;

@VPointOption(name = "丽人-丽人货架商品埋点", description = "多了堆头信息", code = "UnifiedShelfItemBeautyOceanLabsOpt")
public class UnifiedShelfItemBeautyOceanLabsOpt extends UnifiedShelfItemOceanLabsVP<UnifiedShelfItemBeautyOceanLabsOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        Map<String, Object> oceanMap = getCommonOcean(context, param);
        addTopDisplayInfo(oceanMap, param.getProductM(), config);
        addRepurchaseTag(oceanMap, param.getProductM());
        addPurchaseInfo(oceanMap, param.getProductM(), param.getShelfItemVO());
        return JsonCodec.encode(oceanMap);
    }

    private void addTopDisplayInfo(Map<String, Object> oceanMap, ProductM productM, Config config) {
        //是否堆头
        boolean isTopDisplay = ProductMAttrUtils.isTopDisplayProduct(productM);
        //0-普通团购，1-堆头
        oceanMap.put("module_show_type", isTopDisplay ? "1" : "0");
        oceanMap.put("type", ProductTypeEnum.getByType(productM.getProductType()).getDesc());
        String moduleName = Objects.nonNull(config) && StringUtils.isNotBlank(config.getModuleName()) ? config.getModuleName() : "团购货架";
        oceanMap.put("module_name", moduleName);
    }

    private void addRepurchaseTag(Map<String, Object> source, ProductM productM) {
        String repurchaseTag = ProductMAttrUtils.getAttrValue(productM, ProductMAttrUtils.REPURCHASE_TAG);
        source.put("label_name", repurchaseTag != null ? repurchaseTag : "-999");
    }

    private void addPurchaseInfo(Map<String, Object> source, ProductM productM, ShelfItemVO dzItemVO) {
        Map<String, Object> purchaseInfoMap = Maps.newHashMap();
        //是否可购买
        purchaseInfoMap.put("sale_status", dzItemVO.isAvailable() ? "1" : "0");
        //相似品推荐
        String productId = productM.getAttr("dzitem_recommend_product");
        if (StringUtils.isNotBlank(productId)) {
            purchaseInfoMap.put("recommend_product", "1");
            source.put("product_id", productId);
        } else {
            purchaseInfoMap.put("recommend_product", "0");
        }
        source.put("customize_array", Lists.newArrayList(purchaseInfoMap));
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 货架名称
         */
        private String moduleName = "团购货架";
    }

}
