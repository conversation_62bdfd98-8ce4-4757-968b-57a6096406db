package com.sankuai.dzviewscene.product.dealstruct.vo.car;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6 5:47 下午
 */
@MobileDo(id = 0x9d6f)
public class CarDetailModuleVO implements Serializable {
    /**
     * 弹窗信息
     */
    @MobileDo.MobileField(key = 0x76f2)
    private CarDetailPopupItemVO popup;

    /**
     * item列表
     */
    @MobileDo.MobileField(key = 0x987a)
    private List<CarItemVO> itemList;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public CarDetailPopupItemVO getPopup() {
        return popup;
    }

    public void setPopup(CarDetailPopupItemVO popup) {
        this.popup = popup;
    }

    public List<CarItemVO> getItemList() {
        return itemList;
    }

    public void setItemList(List<CarItemVO> itemList) {
        this.itemList = itemList;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}