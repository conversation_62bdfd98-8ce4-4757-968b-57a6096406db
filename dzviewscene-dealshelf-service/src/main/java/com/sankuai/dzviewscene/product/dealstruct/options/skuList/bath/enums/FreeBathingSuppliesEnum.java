package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bath.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 免费洗浴用品
 *
 * @author: created by hang.yu on 2023/10/7 17:05
 */
@Getter
@AllArgsConstructor
public enum FreeBathingSuppliesEnum {

    DISPOSABLE_MATERIALS("disposable_materials", "一次性用品", "https://p0.meituan.net/travelcube/284969b440784da1f6f90718625418711135.png"),
    BATHING_SUPPLIES("bathing_supplies", "洗护用品", "https://p0.meituan.net/travelcube/284969b440784da1f6f90718625418711135.png"),
    ;

    /**
     * 免费洗浴用品code
     */
    private final String code;

    /**
     * 免费洗浴用品名称
     */
    private final String name;

    /**
     * 免费洗浴用品icon
     */
    private final String icon;

}
