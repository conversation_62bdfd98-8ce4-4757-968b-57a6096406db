package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SubProjectNumProcessor implements ValueProcessor {
    //这里简单做了，直接写死
    private static final String SPLIT = "、";

    @Override
    public List<String> process(ValueConfig valueKey, Map<String, String> name2ValueMap, Object data) {
        List<SkuAttrAttrItemVO> attrItemVOS = (List<SkuAttrAttrItemVO>) data;
        if (ObjectUtils.isEmpty(attrItemVOS)) {
            return Lists.newArrayList();
        }
        List<String> projectList = attrItemVOS.stream()
                .filter(v -> !ObjectUtils.isEmpty(v) && !ObjectUtils.isEmpty(v.getValues())).flatMap(v -> v.getValues().stream())
                .filter(v -> !ObjectUtils.isEmpty(v) && !ObjectUtils.isEmpty(v.getName())).map(CommonAttrVO::getName)
                .flatMap(v -> Arrays.stream(v.split(SPLIT))).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(projectList)) {
            return Lists.newArrayList();
        }
        String projectNum = String.valueOf(projectList.size());
        if (!ObjectUtils.isEmpty(valueKey.getFormat())) {
            projectNum = String.format(valueKey.getFormat(), projectNum);
        }
        return Lists.newArrayList(projectNum);
    }

    @Override
    public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor.SubProjectNumProcessor.convertDisplayValues(String,ValueConfig,Map)");
        return null;
    }
}
