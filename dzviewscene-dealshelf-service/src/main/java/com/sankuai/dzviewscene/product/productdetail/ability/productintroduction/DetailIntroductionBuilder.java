package com.sankuai.dzviewscene.product.productdetail.ability.productintroduction;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.mergequery.ProductMergeQueryAbility;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivity;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.utils.CtxUtils;

import java.util.concurrent.CompletableFuture;

@Ability(code = DetailIntroductionBuilder.CODE,
        name = "详情-商品介绍构造",
        description = "详情-商品介绍构造构造能力，构造详情页商品介绍构造内容。主要依赖商品统一融合查询结果，可根据配置对应的不同变化点选项生产不同的商品介绍信息。",
        activities = {
                SpuDetailActivity.CODE
        },
        dependency = {
                ProductMergeQueryAbility.CODE,
        }
)
public class DetailIntroductionBuilder extends PmfAbility<DetailModuleVO<?>, Void, DetailIntroductionCfg> {

    public static final String CODE = "DetailIntroductionBuilder";

    @Override
    public CompletableFuture<DetailModuleVO<?>> build(ActivityCxt ctx, Void param, DetailIntroductionCfg cfg) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.DetailIntroductionBuilder.build(ActivityCxt,Void,DetailIntroductionCfg)");
        DetailModuleVO<?> module = new DetailModuleVO<>();
        module.setModuleKey(cfg.getModuleKey());
        module.setContent(buildContent(ctx));
        return CompletableFuture.completedFuture(module);
    }

    private Content buildContent(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.DetailIntroductionBuilder.buildContent(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        DetailIntroductionVP<?> vp = findVPoint(ctx, DetailIntroductionVP.CODE);
        return vp.execute(ctx, DetailIntroductionVP
                .Param
                .builder()
                .productMs(CtxUtils.getProductsFromCtx(ctx))
                .build()
        );
    }
}