package com.sankuai.dzviewscene.product.shelf.ability.builder.activity;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.ShelfMainDataAssembler;
import com.sankuai.dzviewscene.product.shelf.ability.builder.activity.vp.ActivityEndTimeVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterTipsBarVP;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.productshelf.vu.vo.ActivityComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.TipsBarVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.utils.ShelfErrorUtils;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/4/8
 */
@Ability(code = ActivityComponentBuilder.CODE,
        name = "VO-活动组件构造能力，一般用不上",
        description = "构造 ActivityComponentVO",
        activities = {DealShelfActivity.CODE},
        dependency = {ShelfMainDataAssembler.CODE}
)
public class ActivityComponentBuilder extends PmfAbility<ActivityComponentVO, ActivityComponentBuilder.Request, ActivityComponentBuilder.Config> {

    public static final String CODE = "ActivityComponentBuilder";

    @Override
    public CompletableFuture<ActivityComponentVO> build(ActivityCxt ctx, Request request, Config config) {
        ShelfGroupM shelfGroupM = ctx.getSource(ShelfMainDataAssembler.CODE);
        if (shelfGroupM == null) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.completedFuture(buildActivityComponentVO(ctx, shelfGroupM));
    }

    private ActivityComponentVO buildActivityComponentVO(ActivityCxt activityCxt, ShelfGroupM shelfGroupM) {
        ActivityComponentVO activityComponentVO = new ActivityComponentVO();

        activityComponentVO.setEndTime(buildActivityEndTime(activityCxt, shelfGroupM));
        return activityComponentVO;
    }

    private Long buildActivityEndTime(ActivityCxt activityCxt, ShelfGroupM shelfGroupM) {
        try {
            ActivityEndTimeVP<?> activityEndTimeVP = findVPoint(activityCxt, ActivityEndTimeVP.CODE);
            return activityEndTimeVP.execute(activityCxt,
                    ActivityEndTimeVP.Param.builder().shelfGroupM(shelfGroupM).build());
        } catch (Exception e) {
            ShelfErrorUtils.addBuilderCatError(activityCxt, e);
            return null;
        }
    }


    @AbilityCfg
    @Data
    public static class Config {
    }

    @AbilityRequest
    @Data
    public static class Request {
    }
}
