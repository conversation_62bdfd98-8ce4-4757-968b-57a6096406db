package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.options.DealQueryParallelGrayOpt;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;

import java.util.Map;

public class QueryParallelUtils {

    public static final String DEAL_SHELF_PARALLEL_QUERY = "ParallelQuery_DealShelf";

    public static final String SUCCESS_FILTER_PRODUCT_MATCH = "Success_FilterProductMatch";

    public static final String SUCCESS_FILTER_SELECT_REFRESH = "Success_FilterSelectRefresh";

    public static final String SUCCESS_EMPTY_DATA = "Success_EmptyData";

    public static final String SUCCESS_SKIP_FILTER = "Success_SkipFilter";

    public static final String FALLBACK_PRODUCT_NOT_MATCH = "Fallback_ProductNotMatch";

    public static final String FALLBACK_FILTER_WITHOUT_PRODUCT = "Fallback_FilterWithoutProduct";

    public static final String FALLBACK_PRODUCT_WITHOUT_FILTER = "Fallback_ProductWithoutFilter";

    public static void parallelQueryMonitor(ActivityCxt ctx, String type) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("sceneCode", ctx.getSceneCode());
        tags.put("type", type);
        Cat.logMetricForCount(DEAL_SHELF_PARALLEL_QUERY, tags);
    }

    public static boolean parallelGray(ActivityCxt ctx){
        return ParamsUtil.getBooleanSafely(ctx.getParameters(), DealQueryParallelGrayOpt.CODE);
    }
}
