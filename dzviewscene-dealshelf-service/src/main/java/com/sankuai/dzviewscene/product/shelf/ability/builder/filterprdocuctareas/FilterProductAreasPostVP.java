package com.sankuai.dzviewscene.product.shelf.ability.builder.filterprdocuctareas;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfview.ShelfResponseAssembler;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "货架筛选内容BtnIdAndProAreas后置处理", description = "货架筛选内容BtnIdAndProAreas后置处理", code = FilterProductAreasPostVP.CODE, ability = ShelfResponseAssembler.CODE)
public abstract class FilterProductAreasPostVP<T> extends PmfVPoint<List<FilterBtnIdAndProAreasVO>, FilterProductAreasPostVP.Param, T> {

    public static final String CODE = "FilterProductAreasPostVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<FilterBtnIdAndProAreasVO> btnIdAndProAreasVOS;
    }
}
