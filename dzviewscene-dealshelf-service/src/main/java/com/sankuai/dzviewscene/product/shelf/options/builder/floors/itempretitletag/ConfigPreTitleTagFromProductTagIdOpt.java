package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempretitletag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPreTitleTagVP;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
@VPointOption(name = "基于商品标签配置的标题前标签", description = "根据商品标签ID配置商品标题前标签", code = "ConfigPreTitleTagFromProductTagIdOpt")
public class ConfigPreTitleTagFromProductTagIdOpt extends ItemPreTitleTagVP<ConfigPreTitleTagFromProductTagIdOpt.Config> {

    @Override
    public FloatTagVO compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM == null || MapUtils.isEmpty(config.getProductTagId2TagCfg()) || CollectionUtils.isEmpty(productM.getProductTagList())) {
            return null;
        }
        TagCfg tagCfg = findFirstMatchTagCfg(config.getProductTagId2TagCfg(), productM.getProductTagList());
        return buildFloatTag(tagCfg);
    }

    private TagCfg findFirstMatchTagCfg(Map<String, TagCfg> productTagId2TagCfg, List<TagM> tags) {
        return tags.stream().filter(tag -> productTagId2TagCfg.containsKey(tag.getId()))
                .map(tag -> productTagId2TagCfg.get(tag.getId())).findFirst().orElse(null);
    }

    private FloatTagVO buildFloatTag(TagCfg tagCfg) {
        if (tagCfg == null) {
            return null;
        }
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(tagCfg.getPicUrl());
        dzPictureComponentVO.setAspectRadio(tagCfg.getAspectRadio());
        return new FloatTagVO(dzPictureComponentVO, FloatTagPositionEnums.LEFT_TOP.getPosition());
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 商品标签id对应的标题前标签策略
         */
        private Map<String, TagCfg> productTagId2TagCfg;
    }

    @Data
    public static class TagCfg {
        private String picUrl;
        private double aspectRadio;
    }
}
