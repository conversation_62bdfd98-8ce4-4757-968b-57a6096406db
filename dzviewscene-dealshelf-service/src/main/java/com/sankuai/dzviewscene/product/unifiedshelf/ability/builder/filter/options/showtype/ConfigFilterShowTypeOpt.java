package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.showtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterShowTypeVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.FilterNodeShowTypeEnum;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

@VPointOption(name = "筛选节点样式类型-默认",
        description = "筛选节点样式类型默认实现，配置化控制子项筛选节点样式",
        code = ConfigFilterShowTypeOpt.CODE,
        isDefault = true)
public class ConfigFilterShowTypeOpt extends UnifiedShelfFilterShowTypeVP<ConfigFilterShowTypeOpt.Config> {

    public static final String CODE = "ConfigFilterShowTypeOpt";

    @Override
    public Integer compute(ActivityCxt activityCxt, Param param, Config config) {
        // 配置showType
        if (MapUtils.isNotEmpty(config.getNode2showType()) && config.getNode2showType().get(param.getIdentityName()) != null) {
            return config.getNode2showType().get(param.getIdentityName());
        }
        // 统一控制子tab样式
        if (!param.isOptionNode() && config.getTabShowType() > 0) {
            return config.getTabShowType();
        }
        //默认
        return param.isOptionNode() ? FilterNodeShowTypeEnum.FILTER_GRID_LAYER.getType() : FilterNodeShowTypeEnum.FILTER_ADAPTIVE_GRID_LAYER.getType();
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 统一控制tab子项样式
         */
        private int tabShowType;

        /**
         * 指定节点子项样式配置，Key为节点身份标识，Value为节点showType
         */
        private Map<String, Integer> node2showType;
    }
}
