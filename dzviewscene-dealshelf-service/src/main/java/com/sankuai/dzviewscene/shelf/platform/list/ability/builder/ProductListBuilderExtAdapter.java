package com.sankuai.dzviewscene.shelf.platform.list.ability.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.list.vo.AttrVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.ProductSaleVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.PromoDetailVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.ShopVO;

import java.util.List;

/**
 * Created by float.lu on 2020/9/22.
 */
public abstract class ProductListBuilderExtAdapter implements ProductListBuilderExt {

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.picUrl(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String price(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.price(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String promoTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.promoTag(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public PromoDetailVO promoDetail(ActivityContext activityContext, ProductM productM)  {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.promoDetail(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String activityTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.activityTag(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String discountTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.discountTag(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String categoryTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.categoryTag(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public ShopVO shop(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.shop(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public ProductSaleVO sale(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.sale(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String couponTag(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.couponTag(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public List<AttrVO> attrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter.attrs(ActivityContext,ProductM)");
        return null;
    }

}
