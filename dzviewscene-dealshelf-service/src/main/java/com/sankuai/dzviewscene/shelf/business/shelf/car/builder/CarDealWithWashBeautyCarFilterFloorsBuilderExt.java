package com.sankuai.dzviewscene.shelf.business.shelf.car.builder;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.RichLabel;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/9/11 3:15 下午
 */
@ExtPointInstance(name = "爱车团单货架楼层VO构造扩展点")
public class CarDealWithWashBeautyCarFilterFloorsBuilderExt extends AbstractDefaultFloorsBuilderExt {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.car.deal.shelf.constant.config", defaultValue = "")
    public ConstantConfig constantConfig;

    private static final String OIL_TYPE_ATTR_NAME = "engine_oil_type";

    private static final String CAR_DEAL_SERVICE_TYPE_TAG_ATTR_NAME = "dealServiceTypeAndCategoryNameTagAttr";

    private static final String TITLE_FORMAT = "[%s]%s";

    private static final int ALL_FILTER_TAB_ID = 1;

    /**
     * 洗车适用车型展示的枚举
     */
    private static List<String> SHOW_CAR_TYPE_LIST = Lists.newArrayList("全车型","五座轿车","SUV/MPV","仅SUV", "仅MPV", "无限制");

    private static final String SUB_TITLE = "dealStructSubtitle";

    //精洗团单副标题，取自行业属性
    private static final String DELICACY_SUBTITLE = "delicacy_subtitle";

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        return constantConfig.getDefaultShowProductCount();
    }

    @Override
    public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentTitle(ActivityContext,String,ProductM,long)");
        //加工 title
        String processedTitle = productM.getTitle();
        if (isShowWashCarDealStructTitle(productM, activityContext)) {
            processedTitle = getWashCarStructDealTitle(productM);
        }
        if(filterId == ALL_FILTER_TAB_ID) {
            //全部 Tab 下的 title 有特殊处理逻辑
            return getTitleForAllTab(productM, processedTitle);
        }
        return processedTitle;
    }

    /**
     * 构造全部 Tab 下的特殊标题
     * @param productM
     * @param processedTitle
     * @return
     */
    private String getTitleForAllTab(ProductM productM, String processedTitle) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getTitleForAllTab(ProductM,String)");
        String serviceTypeTag = productM.getAttr(CAR_DEAL_SERVICE_TYPE_TAG_ATTR_NAME);
        if(StringUtils.isEmpty(serviceTypeTag)) {
            return productM.getTitle();
        }
        return String.format(TITLE_FORMAT, serviceTypeTag, processedTitle);
    }

    /**
     * 判断是否需要展示洗车的结构化标题[这里能过，代表一定会产出标题]
     * @param productM
     * @param activityContext
     * @return
     */
    private boolean isShowWashCarDealStructTitle(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.isShowWashCarDealStructTitle(ProductM,ActivityContext)");
        //无适用车型属性
        String carType = getCarType(productM);
        if (StringUtils.isEmpty(carType)) {
            return false;
        }
        //不在展示的枚举中
        if (!SHOW_CAR_TYPE_LIST.contains(carType)) {
            return false;
        }
        //无配置或不在白名单类目中
        if (CollectionUtils.isEmpty(constantConfig.getWashCarTitleServiceType()) || !constantConfig.getWashCarTitleServiceType().contains(ProductMAttrUtils.getServiceType(productM))) {
            return false;
        }
        return DouHuUtils.hitExpectSk(activityContext, "exp001135_b") || DouHuUtils.hitExpectSk(activityContext, "exp001136_b");
    }

    /**
     * 获取适用车型
     * @param productM
     * @return
     */
    private String getCarType(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getCarType(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String carSuitType = productM.getAttr(CarDealShelfWithWashBeautyCarFilterTemplate.ATTR_WASH_CAR_SUIT_TYPE);
        if (Objects.equals(carSuitType, "车型级别限制")) {
            //当为旧的供给时，需要从二级的属性中取值
            return productM.getAttr(CarDealShelfWithWashBeautyCarFilterTemplate.ATTR_CAR_TYPE_LIMIT);
        }
        if (Objects.equals(carSuitType, "无限制")) {
            return "全车型";
        }
        return carSuitType;
    }

    /**
     * 获取洗车团单结构化的标题
     * @param productM
     * @return
     */
    private String getWashCarStructDealTitle(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getWashCarStructDealTitle(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String carType = getCarType(productM);
        return String.format("%s-%s", ProductMAttrUtils.getServiceType(productM), carType);
    }

    @Override
    public String itemComponentRichLabelsTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentRichLabelsTitle(ActivityContext,String,ProductM,long)");
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext,
                itemComponentTitle(activityContext, groupName, productM, filterId),
                ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    @Override
    public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentSalePrice(ActivityContext,String,ProductM)");
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && StringUtils.isNotEmpty(perfectActivityPrice)) {
            return perfectActivityPrice;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return productM.getBasePriceTag();
        }
        return productPromoPriceM.getPromoPriceTag();
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        if (productM == null || StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        return buildPicAreaVO(productM);
    }

    private PicAreaVO buildPicAreaVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildPicAreaVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(productM == null || StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        if(constantConfig == null) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        String picUrl = PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), constantConfig.getPicWidth(), constantConfig.getPitHeight(), PictureURLBuilders.ScaleType.Cut);
        picAreaVO.setPic(buildDzPictureComponentVO(picUrl, 1));
        if(PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            picAreaVO.setFloatTags(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
            return picAreaVO;
        }
        List<ProductActivityM> normalActivityList = PerfectActivityBuildUtils.getNormalActivityList(productM.getActivities());
        picAreaVO.setFloatTags(buildFloatTagVOs(CollectUtils.firstValue(normalActivityList)));
        return picAreaVO;
    }

    @Override
    public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.activityRemainSecondsLabel(ActivityContext,String,ProductM)");
        RichLabelVO richLabelVO = PerfectActivityBuildUtils.buildPerfectActivityRemainSecondsLabel(productM);
        return richLabelVO;
    }

    private List<FloatTagVO> buildFloatTagVOs(ProductActivityM activityM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildFloatTagVOs(com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM)");
        if(activityM == null) {
            return null;
        }
        FloatTagVO floatTagVO = buildFloatTagVO(activityM.getUrl(), activityM.getLable());
        if(floatTagVO == null) {
            return null;
        }
        return Lists.newArrayList(floatTagVO);
    }

    private FloatTagVO buildFloatTagVO(String picUrl, String text) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildFloatTagVO(java.lang.String,java.lang.String)");
        FloatTagVO floatTagVO = new FloatTagVO();
        //优先展示图片
        if(StringUtils.isNotEmpty(picUrl)) {
            floatTagVO.setIcon(buildDzPictureComponentVO(picUrl, constantConfig.getActivityPicAspectRadio()));
            return floatTagVO;
        }
        //其次展示文案
        if(StringUtils.isNotEmpty(text)) {
            RichLabelVO textRichLabelVO = buildRichLabelVO(text, constantConfig.getActivityTextColor(), constantConfig.getActivityTextSize(),
                    constantConfig.getActivityTextPosition(), constantConfig.getActivityTextFontWeight());
            floatTagVO.setLabel(textRichLabelVO);
            return floatTagVO;
        }
        return null;
    }

    private DzPictureComponentVO buildDzPictureComponentVO(String picUrl, double aspectRadio) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildDzPictureComponentVO(java.lang.String,double)");
        if (StringUtils.isEmpty(picUrl)) {
            return null;
        }
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setAspectRadio(aspectRadio);
        dzPictureComponentVO.setPicUrl(picUrl);
        return dzPictureComponentVO;
    }

    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentProductTags(ActivityContext,String,ProductM,long)");
        if (isPreSale(activityContext, productM)) {
            return getPreSaleProductTags(productM);
        }
        if (CollectionUtils.isEmpty(productM.getProductTags()) && isDealInWhiteList(productM.getProductId())) {
            return constantConfig.getMvpSubTitle();
        }
        //精洗团单副标题
        if (StringUtils.isNotEmpty(getJingXiTags(productM))) {
            return Lists.newArrayList(getJingXiTags(productM));
        }
        if (Objects.equals(ProductMAttrUtils.getServiceType(productM), "小保养")) {
            return getXiaoBaoYangTags(productM);
        }
        return productM.getProductTags();
    }

    private boolean isDealInWhiteList(int productId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.isDealInWhiteList(int)");
        return CollectionUtils.isNotEmpty(constantConfig.getShowMvpSubtitleDealIds()) && constantConfig.getShowMvpSubtitleDealIds().contains(productId);
    }

    /**
     * @param productM
     * @return 获取精洗标签
     */
    private String getJingXiTags(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getJingXiTags(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String delicacySubtitle = productM.getAttr(DELICACY_SUBTITLE);
        if (StringUtils.isNotEmpty(delicacySubtitle)){
            return delicacySubtitle;
        }
        return productM.getAttr(SUB_TITLE);
    }

    private List<String> getXiaoBaoYangTags(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getXiaoBaoYangTags(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        List<String> resultTags = new ArrayList<>();
        List<SkuItemDto> mustSkuList = DealStructUtils.getMustSkuList(productM.getAttr("attr_deal_struct_detail"));
        //1、机油粘度
        String thicknessVal = DealStructUtils.getFirstAttrValueByAttrKey(mustSkuList, "thickness");
        addIfNotEmpty(resultTags, thicknessVal);
        if (StringUtils.isEmpty(thicknessVal)) {
            //如果没有机油粘度，则依旧添加机油分类
            addIfNotEmpty(resultTags, DealStructUtils.getFirstAttrValueByAttrKey(mustSkuList, "type"));
        }
        //2、机滤等级
        addIfNotEmpty(resultTags, getBrandOfMaintenanceTag(mustSkuList));
        return resultTags;
    }

    private void addIfNotEmpty(List<String> list, String element) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.addIfNotEmpty(java.util.List,java.lang.String)");
        if (list == null || StringUtils.isEmpty(element)) {
            return;
        }
        list.add(element);
    }

    private String getBrandOfMaintenanceTag(List<SkuItemDto> mustSkuList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.getBrandOfMaintenanceTag(java.util.List)");
        String brand = DealStructUtils.getFirstAttrValueByAttrKey(mustSkuList, "engineOilFilterBrand");
        if (Objects.equals(brand, "品牌机滤")) {
            return brand;
        }
        return null;
    }

    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentPurchase(ActivityContext,String,ProductM)");
        if (productM == null || StringUtils.isBlank(productM.getPurchase())) {
            return null;
        }
        if(PerfectActivityBuildUtils.isDuringPerfectActivityPreheatTime(productM)) {
            return null;
        }
        return buildRichLabelVO(productM.getPurchase(), constantConfig.getPurchaseColor(), constantConfig.getPurchaseSize(), constantConfig.getPurchasePosition(), constantConfig.getPurchaseFontWeight());
    }

    private RichLabelVO buildRichLabelVO(String purchaseText, String color, int size, int position, String fontWeight) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildRichLabelVO(java.lang.String,java.lang.String,int,int,java.lang.String)");
        if (StringUtils.isEmpty(purchaseText)) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(purchaseText);
        richLabelVO.setTextColor(color);
        richLabelVO.setTextSize(size);
        richLabelVO.setPosition(position);
        richLabelVO.setFontWeight(fontWeight);
        return richLabelVO;
    }

    @Override
    public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.itemComponentBottomTags(ActivityContext,String,ProductM)");
        if (productM == null) {
            return null;
        }
        RichLabelVO card = buildRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildRichLabel(productM.getPinPrice());
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        if (card != null) {
            richLabelVOS.add(card);
        }
        if (pin != null) {
            richLabelVOS.add(pin);
        }
        if (CollectionUtils.isEmpty(richLabelVOS)) {
            return null;
        }
        return richLabelVOS;
    }

    private RichLabelVO buildRichLabel(ProductPriceM productPriceM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.buildRichLabel(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM)");
        if (productPriceM == null) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        List<RichLabel> richLabelList = new ArrayList<RichLabel>() {{
            add(new RichLabel(productPriceM.getPriceDesc(), constantConfig.getBottomTagDescColor(), constantConfig.getBottomTagDescSize()));
            add(new RichLabel(productPriceM.getPriceTag(), constantConfig.getBottomTagNumColor(), constantConfig.getBottomTagNumeSize()));
        }};
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, DzItemAreaComponentVO itemAreaComponentVO, long filterId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealWithWashBeautyCarFilterFloorsBuilderExt.moreComponentText(ActivityContext,String,DzItemAreaComponentVO,long)");
        if (itemAreaComponentVO.getProductItems().size() <= constantConfig.getDefaultShowProductCount()) {
            return null;
        }
        return constantConfig.getMorePrefix() + (itemAreaComponentVO.getProductItems().size() - constantConfig.getDefaultShowProductCount()) + constantConfig.getMoreSuffix();
    }

    @Data
    private static class ConstantConfig {
        private int picWidth;
        private int pitHeight;
        private int defaultShowProductCount;
        private String morePrefix;
        private String moreSuffix;
        private String bottomTagNumColor;
        private int bottomTagNumeSize;
        private String bottomTagDescColor;
        private int bottomTagDescSize;
        private String saleFormat;
        private double minOilCapacity;
        private double maxOilCapacity;
        private int purchaseSize;
        private String purchaseColor;
        private int purchasePosition;
        private String purchaseFontWeight;
        private double activityPicAspectRadio;
        private int activityTextSize;
        private String activityTextColor;
        private int activityTextPosition;
        private String activityTextFontWeight;
        /**
         * 展示洗车团单标题的服务类型
         * value：洗车的服务类型
         */
        private List<String> washCarTitleServiceType;
        /**
         * MVP期间，参加活动的团单，在货架展示固定的副标题的团单Id
         */
        private List<Integer> showMvpSubtitleDealIds;
        /**
         * MVP期间，参加活动的团单，在货架展示固定的副标题
         */
        private List<String> mvpSubTitle;

    }

}