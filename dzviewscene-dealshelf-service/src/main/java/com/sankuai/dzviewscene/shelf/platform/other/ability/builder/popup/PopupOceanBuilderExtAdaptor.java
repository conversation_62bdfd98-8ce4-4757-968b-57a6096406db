package com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.business.other.popup.vo.DzPopupOceanVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.springframework.stereotype.Component;

@Component
public class PopupOceanBuilderExtAdaptor implements PopupOceanBuilderExt{

    @Override
    public DzPopupOceanVO oceanVO(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup.PopupOceanBuilderExtAdaptor.oceanVO(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }
}
