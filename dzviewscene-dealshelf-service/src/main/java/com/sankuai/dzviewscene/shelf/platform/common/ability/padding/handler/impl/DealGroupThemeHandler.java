package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.gmkt.activity.api.enums.QuerySecKillSceneStrategyEnum;
import com.dianping.gmkt.event.api.enums.QRCodeType;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.product.shelf.common.enums.ShelfItemUnitTypeEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dztheme.deal.dto.*;
import com.sankuai.dztheme.deal.dto.enums.ThemeReqSourceEnum;
import com.sankuai.dztheme.deal.dto.sku.DealProductSkuDTO;
import com.sankuai.dztheme.deal.req.DealMaterialReq;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.PaddingConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.shelf.utils.*;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.utils.UnifiedShelfOperatorConfigUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.product.utils.LionObjectManagerUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.GroupPaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.*;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.it.iam.common_base.utils.IntegerUtil;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 团单商品填充器
 * <p>
 * Created by float.lu on 2020/9/1.
 */
@Component
@Slf4j
public class DealGroupThemeHandler implements GroupPaddingHandler {

    private static final int PADDING_DEAL_LIMIT = 100;
    private static final String ANDROID = "android";
    private static final String HARMONY = "harmony";
    private static final int OS_TYPE_HARMONY_INT = 3;
    public static final int OS_TYPE_ANDROID_INT = 2;
    public static final int OS_TYPE_IOS_INT = 1;
    public static final String SERVICE_TYPE_LEAF_ID = "service_type_leaf_id";

    @Resource
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.deal.theme.rpc.service.new.switch", defaultValue = "false")
    private boolean dealThemeRpcServiceNewSwitch;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.deal.theme.rpc.service.new.scenecode.list", defaultValue = "[]")
    private List<String> dealThemeRpcServiceNewSceneCodeList;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.deal.theme.rpc.pagesource.scene.white.list", defaultValue = "{}")
    private Map<String, List<String>> pricePageSourceSceneWhiteList;

    private static final String GAME_COIN_SCENE_CODE = "game_room_coin_deal_shelf";

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts()) || MapUtils.isEmpty(params)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        // 神会员参数上报
        magicMemberReport(activityContext);

        if (usePrePadding(activityContext, productGroupM)) {
            //走预填充合并
            PaddingParallelUtils.parallelPaddingMonitor(activityContext, productGroupM, ProductTypeEnum.DEAL.getType());
            return CompletableFuture.completedFuture(paddingByPrePaddingResult(productGroupM));
        }

        String planId = (String) params.get(PaddingFetcher.Params.planId);
        if (StringUtils.isEmpty(planId)) {
            throw new BusinessException("填充失败, planId缺失");
        }
        // 调用主题
        CompletableFuture<List<DealProductResult>> dealProductResultListFuture = batchQueryProductTheme(activityContext, productGroupM, params, planId);
        return dealProductResultListFuture.thenApply(dealProductResultList -> {
            LogUtils.recordKeyMsg(getUserId(activityContext), "queryDealProductThemeEnd", dealProductResultList);
            ProductMUtils.asyncDiffPaddingPreHandler(activityContext, productGroupM, ProductTypeEnum.DEAL.getType());
            // 填充
            dealProductResultList.forEach(dealProductResult -> paddingWithDealProductResult(productGroupM, dealProductResult));
            // 神会员流量染色
            putMagicalMemberDyeContext(productGroupM, activityContext);
            ProductMUtils.asyncDiffPrePadding(activityContext, productGroupM, ProductTypeEnum.DEAL.getType());
            return productGroupM;
        });
    }

    private void magicMemberReport(ActivityContext activityContext) {
        String path = MagicMemberUtil.getPath(activityContext.getParameters());
        if (StringUtils.isBlank(path) || !MagicMemberUtil.addReportFlag(activityContext)) {
            // 非货架/落地页场景，不需要上报；本次请求已经上报了，不需要上报
            return;
        }
        MagicMemberUtil.reportParam(path, activityContext.getParameters());
        MagicMemberUtil.setRegionToTrace(activityContext.getParameters());
    }

    private boolean usePrePadding(ActivityContext activityContext, ProductGroupM productGroupM) {
        //diff不走预填充
        return !enablePaddingDiff(activityContext) && MapUtils.isNotEmpty(productGroupM.getPreLoadProducts()) &&
                CollectionUtils.isNotEmpty(productGroupM.getPreLoadProducts().get(ProductTypeEnum.DEAL.getType()));
    }

    private boolean enablePaddingDiff(ActivityContext activityContext){
        boolean enablePaddingDiff = ParamsUtil.getBooleanSafely(activityContext, PaddingFetcher.Params.enablePaddingDiff);
        return enablePaddingDiff || ProductMUtils.enableProductDiff(activityContext, ProductTypeEnum.DEAL.getType());
    }

    private ProductGroupM paddingByPrePaddingResult(ProductGroupM productGroupM) {
        List<ProductM> preProductMS = productGroupM.getPreLoadProducts().get(ProductTypeEnum.DEAL.getType());
        Map<Integer, ProductM> preProductMap = preProductMS.stream()
                .collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), productM), HashMap::putAll);
        List<ProductM> productMS = Lists.newArrayList();
        productGroupM.getProducts().forEach(productM -> {
                paddingByPreProductM(productM, preProductMap.get(productM.getProductId()));
                if(productM.getProductType() == ProductTypeEnum.DEAL.getType()
                        && !productM.isPrePadded()){
                    Cat.logEvent("DealGroupThemeHandler","padding.miss");
                    return;
                }
                productMS.add(productM);
            }
        );
        productGroupM.setProducts(productMS);
        return productGroupM;
    }

    private CompletableFuture<List<DealProductResult>> batchQueryProductTheme(ActivityContext activityContext, ProductGroupM productGroupM,
                                                                              Map<String, Object> params, String planId) {

        if (CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<CompletableFuture<DealProductResult>> paddingResultFutureList = new ArrayList<>();
        int startIndex = 0;
        int paddingLimit = getPaddingDealLimit(params);
        int fetchCount = (int) Math.ceil(productGroupM.getProducts().size() / (paddingLimit * 1.0));
        for (int i = startIndex; i < fetchCount; i++) {
            int endIndex = Math.min((i + 1) * paddingLimit, productGroupM.getProducts().size());
            List<ProductM> products = productGroupM.getProducts().subList(i * paddingLimit, endIndex);
            DealProductRequest dealProductRequest = buildDealProductRequest(activityContext, productGroupM, planId, products, params);
            CompletableFuture<DealProductResult> productResultCompletableFuture;
            LogUtils.recordKeyMsg(getUserId(activityContext),"queryDealProductThemeStart",dealProductRequest);
            productResultCompletableFuture = compositeAtomService.queryDealProductTheme(dealProductRequest);

            addInvokeTraceElement(activityContext, System.currentTimeMillis(), productResultCompletableFuture, dealProductRequest);
            paddingResultFutureList.add(productResultCompletableFuture);
        }
        return assemble(paddingResultFutureList);
    }

    private int getPaddingDealLimit(Map<String, Object> params) {
        if (MapUtils.isNotEmpty(params) && params.containsKey(PaddingConstants.Params.batchSize)) {
            return (Integer) params.get(PaddingConstants.Params.batchSize);
        }
        return PADDING_DEAL_LIMIT;
    }

    private void addInvokeTraceElement(ActivityContext activityContext, long startTime, CompletableFuture<DealProductResult> productResultCompletableFuture, DealProductRequest dealProductRequest) {
        if (productResultCompletableFuture == null || !activityContext.isTrace()) {
            return;
        }
        productResultCompletableFuture.whenComplete((result, throwable) -> {
            try {
                activityContext.addTrace(TraceElement.build(
                        "业务点",
                        "团单主题填充",
                        JacksonUtils.serialize(dealProductRequest),
                        result,
                        throwable,
                        System.currentTimeMillis() - startTime
                ));
            } catch (Exception e) {

            }
        });
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private void paddingWithDealProductResult(ProductGroupM productGroupM, DealProductResult dealProductResult) {
        if (dealProductResult == null || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return;
        }
        List<DealProductDTO> dealProductDTOs = dealProductResult.getDeals();

        Map<Integer, DealProductDTO> dealProducts = dealProductDTOs.stream().filter(Objects::nonNull)
                .collect(HashMap::new, (map, dealDTO) -> map.put(dealDTO.getProductId(), dealDTO), HashMap::putAll);
        if (Objects.isNull(productGroupM) || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return;
        }
        productGroupM.getProducts().forEach(productM -> paddingProductM(productM, dealProducts.get(productM.getProductId())));
    }

    public ProductM buildDealProductM(DealProductDTO dealProductDTO) {
        ProductM productM = new ProductM();
        paddingProductM(productM, dealProductDTO);
        return productM;
    }

    public List<ProductM> buildDealProductMs(List<DealProductDTO> dealProductDTOs) {
        List<ProductM> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(dealProductDTOs)) {
            return result;
        }
        for (DealProductDTO dealProductDTO : dealProductDTOs) {
            result.add(buildDealProductM(dealProductDTO));
        }
        return result;
    }

    private void paddingProductM(ProductM productM, DealProductDTO dealProductDTO) {
        if (dealProductDTO == null) {
            // miss
            return;
        }
        // 1. 团单ID
        productM.setProductId(dealProductDTO.getProductId());
        // 2. 团单标题
        productM.setTitle(dealProductDTO.getName());
        // 3. 团单详情跳转URL
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 4. 团单销量
        productM.setSale(buildProductSale(dealProductDTO.getSale()));
        // 5. 团单售卖价格标签
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 6. 团单售卖价格
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 7. 团单市场价格
        productM.setMarketPrice(dealProductDTO.getMarketPriceTag());
        // 8. 融合优惠啊
        productM.setPromoPrices(buildDealPromoPrice(productM, dealProductDTO.getPromoPrices()));
        // 9. 团单购买信息
        productM.setPurchase(dealProductDTO.getPurchase());
        // 10. 团单拼团标签
        productM.setPinPrice(buildProductPriceM(dealProductDTO.getPinPrice()));
        // 11. 团单次卡标签
        productM.setCardPrice(buildProductPriceM(dealProductDTO.getCardPrice()));
        // 12. 团单跳转链接
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 13. 团单标准商品标签
        productM.setProductTags(dealProductDTO.getProductTags());
        // 13. 团单商品头图
        productM.setPicUrl(dealProductDTO.getHeadPic());
        // 14. 团单扩展属性
        paddingProductMAttrs(dealProductDTO, productM);
        // 15. 关联商户信息
        productM.setShopMs(buildShopMs(productM, dealProductDTO));
        // 16、消费返券
        productM.setCoupons(buildCoupons(dealProductDTO.getCoupons()));
        //17.商品原始售卖价格
        productM.setBasePrice(dealProductDTO.getBasePrice());
        //18.商品类目
        productM.setCategoryId(dealProductDTO.getCategoryId());
        productM.setCategoryName(dealProductDTO.getCategory());
        //19.商品活动
        productM.setActivities(buildDealActivities(dealProductDTO.getActivities()));
        //20.团单售卖价格描述
        productM.setBasePriceDesc(dealProductDTO.getBasePriceDesc());
        //21.库存
        paddingProductMStock(dealProductDTO, productM);
        //22.标签列表
        productM.setProductTagList(buildProductTags(dealProductDTO.getProductTagList()));
        productM.setBeginDate(dealProductDTO.getBeginDate());
        productM.setEndDate(dealProductDTO.getEndDate());
        //23.商品关联的订单信息
        productM.setOrderUsers(buildOrderUsers(dealProductDTO.getOrderUsers()));
        //24.下单链接
        productM.setOrderUrl(dealProductDTO.getOrderUrl());

        //25.SPU
        productM.setSpuM(dealProductDTO.getSpu());
        productM.setSpuMList(dealProductDTO.getSpuList());

        //标记下已题填充
        productM.setPrePadded(true);

        // 26.rank
        productM.setResourceRank(buildRank(dealProductDTO));
        // 27.扩展商品图片信息
        productM.setExtendImages(buildExtendImages(dealProductDTO.getMultiRatioHeadPicDTO()));

        // 28. 团单是否为太极团单
        productM.setUnifyProduct(dealProductDTO.isUnifyProduct());
        // 29. 团单交易类型
        productM.setTradeType(dealProductDTO.getTradeType());
        // 30. 团单使用规则
        productM.setUseRuleM(buildUseRule(dealProductDTO.getUseRule()));
        productM.setMaterialList(buildMaterialList(dealProductDTO.getMaterialList(), productM));
        // 服务项目
        productM.setDealDetailStructuredDTO(dealProductDTO.getDealDetailStructuredDTO());
        productM.setStandardServiceProjectDTO(dealProductDTO.getStandardServiceProjectDTO());
        // 三级类目ID
        productM.setServiceTypeId(dealProductDTO.getServiceTypeId());


        if(CollectionUtils.isNotEmpty(productM.getMaterialList())){
            productM.setSale(null);
            DealProductMaterialM materialM = productM.getMaterialList().get(0);
            if(materialM.getSale() > 0) {
                ProductSaleM productSaleM = new ProductSaleM();
                productSaleM.setSale((int) materialM.getSale());
                productSaleM.setSaleTag(materialM.getSaleTag());
                productM.setSale(productSaleM);
            }
        }
        // 31. 团单加项列表
        productM.setAdditionalProjectList(buildAdditionalProjectList(dealProductDTO.getAdditionalProjectList()));
        // 32. 团单品牌名称
        productM.setBrandName(dealProductDTO.getBrandName());
        // 33. 团单sku列表
        productM.setProductSkuList(buildSkuList(dealProductDTO.getSkuList(), dealProductDTO.getProductId()));
    }

    private List<ProductSkuM> buildSkuList(List<DealProductSkuDTO> skuList, long productId) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        return skuList.stream().filter(Objects::nonNull).map(sku -> {
            ProductSkuM productSkuM = new ProductSkuM();
            productSkuM.setProductId(sku.getProductId());
            productSkuM.setSkuId(sku.getSkuId());
            productSkuM.setSkuName(sku.getName());
            productSkuM.setJumpUrl(sku.getDetailUrl());
            if(sku.getPrice() != null){
                productSkuM.setMarketPrice(Optional.ofNullable(sku.getPrice().getMarketPrice())
                        .map(BigDecimal::toString)
                        .orElse(null));
                productSkuM.setSalePrice(Optional.ofNullable(sku.getPrice().getSalePrice())
                        .map(BigDecimal::toString)
                        .orElse(null));
                productSkuM.setPromoPrice(Optional.ofNullable(sku.getPrice().getPromoPrice())
                        .map(BigDecimal::toString)
                        .orElse(null));
                productSkuM.setPromoPrices(buildPromoPrice(sku.getPromoPrices(), productSkuM.getMarketPrice(), productId));
            }
            return productSkuM;
        }).collect(Collectors.toList());
    }

    private List<DealAdditionalProjectM> buildAdditionalProjectList(List<DealAdditionalProjectDTO> additionalProjectList) {
        if (CollectionUtils.isEmpty(additionalProjectList)) {
            return Lists.newArrayList();
        }
        return additionalProjectList.stream().filter(Objects::nonNull).map(additionalProject -> {
            DealAdditionalProjectM additionalProjectM = new DealAdditionalProjectM();
            additionalProjectM.setProductId(additionalProject.getProductId());
            additionalProjectM.setSkuId(additionalProject.getSkuId());
            additionalProjectM.setProductType(additionalProject.getProductType());
            additionalProjectM.setItemName(additionalProject.getItemName());
            additionalProjectM.setMarketPrice(additionalProject.getMarketPrice());
            additionalProjectM.setSalePrice(additionalProject.getSalePrice());
            additionalProjectM.setSalesCnt(additionalProject.getSalesCnt());
            additionalProjectM.setClassification(additionalProject.getClassification());
            if (CollectionUtils.isNotEmpty(additionalProject.getProductTagList())) {
                Map<String, String> productTagMap = Maps.newLinkedHashMap();
                additionalProject.getProductTagList().forEach(productTag -> productTagMap.put(productTag.getTagKey(), productTag.getTagValue()));
                additionalProjectM.setProductTagMap(productTagMap);
            }
            return additionalProjectM;
        }).collect(Collectors.toList());
    }

    private List<ExtendImageM> buildExtendImages(MultiRatioHeadPicDTO multiRatioHeadPicDTO) {
        if (multiRatioHeadPicDTO == null || CollectionUtils.isEmpty(multiRatioHeadPicDTO.getMultiRatioHeadPics())) {
            return Lists.newArrayList();
        }
        return multiRatioHeadPicDTO.getMultiRatioHeadPics().stream().map(extendImageItem -> {
            ExtendImageM extendImageM = new ExtendImageM();
            extendImageM.setUrl(extendImageItem.getUrl());
            extendImageM.setRatio(extendImageItem.getRatio());
            return extendImageM;
        }).collect(Collectors.toList());
    }

    private ResourceRankM buildRank(DealProductDTO dealProductDTO) {
        if (CollectionUtils.isEmpty(dealProductDTO.getAttrs())) {
            return null;
        }
        Optional<DealProductAttrDTO> rankName = dealProductDTO.getAttrs().stream().filter(o -> Objects.equals(o.getName(), ConstantUtils.RANK_LABEL_NAME_KEY)).findFirst();
        Optional<DealProductAttrDTO> rankLink = dealProductDTO.getAttrs().stream().filter(o -> Objects.equals(o.getName(), ConstantUtils.RANK_LABEL_LINK_KEY)).findFirst();

        ResourceRankM resourceRankM = new ResourceRankM();
        resourceRankM.setRankName(rankName.isPresent() ? rankName.get().getValue() : "");
        resourceRankM.setRankLinkUrl(rankLink.isPresent() ? rankLink.get().getValue() : "");
        return resourceRankM;

    }

    private void paddingByPreProductM(ProductM productM, ProductM preProductM) {
        if (preProductM == null) {
            // miss
            return;
        }
        mergePromoPrice(productM, preProductM);
        ProductMUtils.copyProductM(productM, preProductM);
    }

    private void mergePromoPrice(ProductM productM, ProductM preProductM) {
        if (productM == null || preProductM == null || CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return;
        }
        //如果调用主题之前已经填充，则替换为已填充的优惠
        preProductM.setPromoPrices(productM.getPromoPrices());
    }

    private List<OrderUserM> buildOrderUsers(List<OrderUserDTO> orderUsers) {
        if (CollectionUtils.isEmpty(orderUsers)) {
            return new ArrayList<>();
        }
        return orderUsers.stream().filter(Objects::nonNull).map(orderUser -> {
            OrderUserM orderUserM = new OrderUserM();
            BeanUtils.copyProperties(orderUser, orderUserM);
            return orderUserM;
        }).collect(Collectors.toList());
    }

    private List<TagM> buildProductTags(List<TagDTO> productTagList) {
        if (CollectionUtils.isEmpty(productTagList)) {
            return new ArrayList<>();
        }
        return productTagList.stream().filter(Objects::nonNull)
                .map(dto -> new TagM(dto.getType(), dto.getId(), dto.getName()))
                .collect(Collectors.toList());
    }

    private void paddingProductMStock(DealProductDTO themeProductDTO, ProductM productM) {
        DealProductStockDTO productStockDTO = themeProductDTO.getStock();
        if (productStockDTO == null) {
            return;
        }
        ProductStockM stockM = new ProductStockM();
        stockM.setRemainStock(productStockDTO.getRemain());
        stockM.setTotalStock(productStockDTO.getTotal());
        stockM.setSoldOut(productStockDTO.isSoldOut());
        productM.setStock(stockM);
    }

    private List<ProductActivityM> buildDealActivities(List<DealProductActivityDTO> dealProductActivityDTOs) {
        if (CollectionUtils.isEmpty(dealProductActivityDTOs)) {
            return Lists.newArrayList();
        }
        return dealProductActivityDTOs.stream().filter(dealProductActivityDTO -> dealProductActivityDTO != null).map(dealProductActivityDTO -> {
            ProductActivityM productActivityM = new ProductActivityM();
            productActivityM.setRemainingTime(dealProductActivityDTO.getRemainingTime());
            productActivityM.setUrl(dealProductActivityDTO.getUrl());
            productActivityM.setLable(dealProductActivityDTO.getTitle());
            productActivityM.setActivityBeginTime(dealProductActivityDTO.getActivityBeginTime());
            productActivityM.setActivityEndTime(dealProductActivityDTO.getActivityEndTime());
            productActivityM.setActivityPriceColor(dealProductActivityDTO.getActivityPriceColor());
            productActivityM.setActivityPriceText(dealProductActivityDTO.getActivityPriceText());
            productActivityM.setShelfActivityType(dealProductActivityDTO.getShelfActivityType());
            productActivityM.setPreheat(dealProductActivityDTO.isPreheat());
            productActivityM.setPageId(dealProductActivityDTO.getPageId());
            productActivityM.setActivityScene(dealProductActivityDTO.getActivityScene());
            productActivityM.setPriceStrengthTime(dealProductActivityDTO.getPriceStrengthTime());
            productActivityM.setUrlAspectRadio(dealProductActivityDTO.getUrlAspectRadio());
            productActivityM.setActivityExtraAttrs(dealProductActivityDTO.getActivityExtraAttrs());
            productActivityM.setActivityPicUrlMap(dealProductActivityDTO.getPicUrlDTOMap());
            productActivityM.setExposurePromotionType(dealProductActivityDTO.getExposurePromotionType());
            return productActivityM;
        }).collect(Collectors.toList());
    }

    private List<ShopM> buildShopMs(ProductM productM, DealProductDTO productDTO) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (CollectionUtils.isNotEmpty(productM.getShopMs())) {
            return productM.getShopMs();
        }
        if (CollectionUtils.isEmpty(productDTO.getShops())) {
            return Lists.newArrayList();
        }
        return productDTO.getShops().stream().map(shop -> {
            ShopM shopM = new ShopM();
            shopM.setShopId(shop.getShopId());
            shopM.setShopName(shop.getShopName());
            shopM.setLongShopId(shop.getShopIdAsLong());
            shopM.setShopUuid(shop.getShopUuid());
            shopM.setLat(shop.getLat());
            shopM.setLng(shop.getLng());
            shopM.setAddress(shop.getAddress());
            shopM.setDistance(shop.getDistance());
            shopM.setDistanceNum(shop.getDistanceNum());
            shopM.setPic(shop.getHeadPic());
            shopM.setLabels(buildShopLabels(shop.getLabels()));
            shopM.setDetailUrl(shop.getShopUrl());
            shopM.setMainRegionName(shop.getBusinessRegionName());
            shopM.setStarStr(shop.getStarStr());
            shopM.setUserEqShop(userEqShop(shop.getRemote()));
            if (shop.getProductSale() != null) {
                ProductSaleM productSaleM = new ProductSaleM();
                productSaleM.setSale(shop.getProductSale().getSale());
                productSaleM.setSaleTag(shop.getProductSale().getSaleTag());
                shopM.setSale(productSaleM);
            }
            return shopM;
        }).collect(Collectors.toList());
    }

    private boolean userEqShop(RemoteDTO remoteDTO) {
        if (remoteDTO == null) {
            return false;
        }
        return remoteDTO.isUserEqShop();
    }

    private List<ShopLabelM> buildShopLabels(List<ShopLabelDTO> labels) {
        List<ShopLabelM> shopLabelMS = new ArrayList<>();
        if (CollectionUtils.isEmpty(labels)) {
            return shopLabelMS;
        }
        labels.forEach(shopLabelDTO -> {
            ShopLabelM shopLabelM = new ShopLabelM();
            shopLabelM.setType(shopLabelDTO.getType());
            shopLabelM.setTitle(shopLabelDTO.getTitle());
            shopLabelMS.add(shopLabelM);
        });
        return shopLabelMS;
    }

    private List<ProductCouponM> buildCoupons(List<DealProductCouponDTO> couponDTOS) {
        if (CollectionUtils.isEmpty(couponDTOS)) {
            return null;
        }
        return couponDTOS.stream().filter(couponDTO -> couponDTO != null).map(couponDTO -> {
            ProductCouponM productCouponM = new ProductCouponM();
            productCouponM.setCouponTag(couponDTO.getBonusTag());
            return productCouponM;
        }).collect(Collectors.toList());
    }

    private ProductPriceM buildProductPriceM(DealProductPriceDTO productPriceDTO) {
        if (productPriceDTO == null) {
            return null;
        }
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag(productPriceDTO.getPriceTag());
        productPriceM.setPriceDesc(productPriceDTO.getPriceDesc());
        return productPriceM;
    }

    private List<ProductPromoPriceM> buildDealPromoPrice(ProductM productM, List<DealProductPromoDTO> promoDTOs) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (CollectionUtils.isNotEmpty(productM.getPromoPrices())) {
            Cat.logEvent("DealGroupThemeHandler", "other promoPrice");
            return productM.getPromoPrices();
        }
        if (CollectionUtils.isEmpty(promoDTOs)) {
            return Lists.newArrayList();
        }
        return buildPromoPrice(promoDTOs, productM.getMarketPrice(), productM.getProductId());
    }

    private List<ProductPromoPriceM> buildPromoPrice(List<DealProductPromoDTO> promoDTOs, String marketPrice, long productId) {
        return promoDTOs.stream().filter(promoDTO -> promoDTO != null).map(promoDTO -> {
            ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
            productPromoPriceM.setProductId(productId);
            productPromoPriceM.setPromoType(promoDTO.getPromoType());
            productPromoPriceM.setPromoPrice(promoDTO.getPromoPrice());
            productPromoPriceM.setPromoPriceTag(promoDTO.getPromoPriceTag());
            productPromoPriceM.setNationalSubsidyPrice(promoDTO.getNationalSubsidyPrice());
            productPromoPriceM.setDiscount(promoDTO.getDiscount());
            productPromoPriceM.setPromoTag(promoDTO.getPromoTag());
            productPromoPriceM.setDiscountTag(promoDTO.getDiscountTag());
            productPromoPriceM.setAvailableTime(promoDTO.getAvailableTime());
            productPromoPriceM.setTotalPromoPrice(promoDTO.getTotalPromoPrice());
            productPromoPriceM.setTotalPromoPriceTag(promoDTO.getTotalPromoPriceTag());
            productPromoPriceM.setPromoItemList(buildPromoItemList(promoDTO.getPromoItemList()));
            productPromoPriceM.setMarketPrice(marketPrice);
            productPromoPriceM.setNationalSubsidyMarketPrice(promoDTO.getMarketPrice());
            productPromoPriceM.setPromoTagType(promoDTO.getPromoTagType());
            productPromoPriceM.setIcon(promoDTO.getPromoTagIcon());
            productPromoPriceM.setIconText(promoDTO.getPromoTagIconText());
            productPromoPriceM.setSinglePrice(promoDTO.getSinglePrice());
            productPromoPriceM.setPricePromoInfoMap(promoDTO.getPricePromoInfoMap());
            productPromoPriceM.setExtendDisplayInfo(promoDTO.getExtendDisplayInfo());
            return productPromoPriceM;
        }).collect(Collectors.toList());
    }

    private List<PromoItemM> buildPromoItemList(List<DealPromoItemDTO> promoItemList) {
        if (CollectionUtils.isEmpty(promoItemList)) {
            return null;
        }
        return promoItemList.stream().map(DealGroupThemeHandler::buildPromoItemM).collect(Collectors.toList());
    }

    public static PromoItemM buildPromoItemM(DealPromoItemDTO promoItem) {
        if (promoItem == null) {
            return null;
        }
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoId(promoItem.getPromoId());
        promoItemM.setPromoTypeCode(promoItem.getPromoTypeCode());
        promoItemM.setDesc(promoItem.getDesc());
        promoItemM.setPromoPrice(promoItem.getPromoPrice());
        promoItemM.setAmount(promoItem.getAmount());
        promoItemM.setCouponId(promoItem.getCouponId());
        promoItemM.setCouponGroupId(promoItem.getCouponGroupId());
        promoItemM.setPromoTag(promoItem.getPromoTag());
        promoItemM.setPromoType(promoItem.getPromoType());
        promoItemM.setPromoIdentity(promoItem.getPromoIdentity());
        promoItemM.setSourceType(promoItem.getSourceType());
        promoItemM.setIcon(promoItem.getIcon());
        promoItemM.setNewUser(promoItem.isNewUser());
        promoItemM.setMinConsumptionAmount(promoItem.getMinConsumptionAmount());
        promoItemM.setRemainStock(promoItem.getRemainStock());
        promoItemM.setEndTime(promoItem.getEndTime());
        promoItemM.setEffectiveEndTime(promoItem.getEffectiveEndTime());
        promoItemM.setPromoItemText(convertPromoItemText(promoItem.getPromoItemTextDTO()));
        promoItemM.setPromotionExplanatoryTags(promoItem.getPromotionExplanatoryTags());
        promoItemM.setPromotionOtherInfoMap(promoItem.getPromotionOtherInfoMap());
        return promoItemM;
    }

    private static PromoItemTextM convertPromoItemText(DealPromoItemTextDTO dealPromoItemTextDTO){
        if (dealPromoItemTextDTO == null){
            return null;
        }
        PromoItemTextM promoItemTextM = new PromoItemTextM();
        promoItemTextM.setTitle(dealPromoItemTextDTO.getTitle());
        promoItemTextM.setSubTitle(dealPromoItemTextDTO.getSubTitle());
        promoItemTextM.setIcon(dealPromoItemTextDTO.getIcon());
        promoItemTextM.setPromoDivideType(dealPromoItemTextDTO.getPromoDivideType());
        promoItemTextM.setPromoDivideTypeDesc(dealPromoItemTextDTO.getPromoDivideTypeDesc());
        promoItemTextM.setPromoStatusText(dealPromoItemTextDTO.getPromoStatusText());
        promoItemTextM.setAtmosphereBarText(dealPromoItemTextDTO.getAtmosphereBarText());
        promoItemTextM.setAtmosphereBarIcon(dealPromoItemTextDTO.getAtmosphereBarIcon());
        promoItemTextM.setAtmosphereBarButtonText(dealPromoItemTextDTO.getAtmosphereBarButtonText());
        promoItemTextM.setAtmosphereBarButtonUrl(dealPromoItemTextDTO.getAtmosphereBarButtonUrl());
        return promoItemTextM;
    }

    private ProductSaleM buildProductSale(DealProductSaleDTO saleDTO) {
        if (saleDTO == null) {
            return null;
        }
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(saleDTO.getSale());
        productSaleM.setSaleTag(saleDTO.getSaleTag());
        return productSaleM;
    }

    private void paddingProductMAttrs(DealProductDTO themeProductDTO, ProductM productM) {
        List<AttrM> themeAttrs = collectThemeAttrs(themeProductDTO);
        mergeThemeAttrs2ProductMAttrs(productM, themeAttrs);
    }

    private void mergeThemeAttrs2ProductMAttrs(ProductM productM, List<AttrM> themeAttrs) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList());
        }
        productM.getExtAttrs().addAll(themeAttrs);
    }

    private List<AttrM> collectThemeAttrs(DealProductDTO themeProductDTO) {
        if (themeProductDTO == null || CollectionUtils.isEmpty(themeProductDTO.getAttrs())) {
            return Lists.newArrayList();
        }
        return themeProductDTO.getAttrs().stream().filter(attr -> attr != null).map(attr -> new AttrM(attr.getName(), attr.getValue(), attr.getValueList())).collect(Collectors.toList());
    }

    private DealProductRequest buildDealProductRequest(ActivityContext activityContext, ProductGroupM productGroupM, String planId, List<ProductM> products, Map<String, Object> params) {
        List<Integer> dealIds = products.stream().map(ProductM::getProductId).distinct().collect(Collectors.toList());
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setPlanId(planId);
        dealProductRequest.setProductIds(dealIds);
        dealProductRequest.setExtParams(buildQueryExtParams(activityContext, productGroupM, dealIds, params, products));
        return dealProductRequest;
    }

    private Map<String, Object> buildQueryExtParams(ActivityContext activityContext, ProductGroupM productGroupM, List<Integer> dealIds, Map<String, Object> params, List<ProductM> products) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        int dpCityId = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        int mtCityId = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        long dpUserId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        long mtUserId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtUserId);
        long mtVirtualUserId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtVirtualUserId);
        long dpShopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        long mtShopId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        Integer directPromoSceneCode = getDirectPromoSceneCode(params, buildExpSks(activityContext));

        Map<String, Object> extParams = new HashMap<>();
        extParams.put("dealIds", dealIds);
        // 1. 点评商户ID
        extParams.put("dpShopId", Long.valueOf(dpShopId).intValue());
        extParams.put("dpShopIdForLong", dpShopId);
        // 2. 点评1, 美团2
        extParams.put("platform", PlatformUtil.getPlatform(platform));
        // 3. 城市ID, 点评侧为点评城市ID, 美团侧为美团城市ID
        extParams.put("cityId", PlatformUtil.isMT(platform) ? mtCityId : dpCityId);
        extParams.put("mtCityId", mtCityId);
        // 用户定位城市id，区分环境
        extParams.put("localCityId", activityContext.getParam(ShelfActivityConstants.Params.locationCityId));
        extParams.put("mtLocalCityId", activityContext.getParam(ShelfActivityConstants.Params.mtLocationCityId));
        // 商户所在城市id，区分环境
        extParams.put("shopDpCityId", activityContext.getParam(ShelfActivityConstants.Params.shopDpCityId));
        extParams.put("shopMtCityId", activityContext.getParam(ShelfActivityConstants.Params.shopMtCityId));
        // 4. 用户ID, 点评侧为点评用户ID, 美团侧为美团用户ID
        extParams.put("userId", PlatformUtil.isMT(platform) ? mtUserId : dpUserId);
        extParams.put(PmfConstants.Params.mtUserId, mtUserId);
        extParams.put(PmfConstants.Params.dpUserId, dpUserId);
        extParams.put("mtVirtualUserId", mtVirtualUserId);
        // 5. 设备ID
        extParams.put("deviceId", activityContext.getParam(ShelfActivityConstants.Params.deviceId));
        // 6. 经纬度
        extParams.put("lat", activityContext.getParam(ShelfActivityConstants.Params.lat));
        extParams.put("lng", activityContext.getParam(ShelfActivityConstants.Params.lng));
        extParams.put("coordType", activityContext.getParam(ShelfActivityConstants.Params.coordType));
        // 7. app版本
        extParams.put("appVersion", activityContext.getParam(ShelfActivityConstants.Params.appVersion));
        // 8. 门店ID
        extParams.put("shopId", PlatformUtil.isMT(platform) ? (int) mtShopId : (int) dpShopId);
        extParams.put("shopIdForLong", PlatformUtil.isMT(platform) ? mtShopId : dpShopId);
        //不需要强转int，主题层使用是long类型mtShopId
        extParams.put("mtShopId", mtShopId);
        extParams.put("mtShopIdForLong", mtShopId);
        // 9. 客户端类型
        extParams.put("clientType", activityContext.getParam(ShelfActivityConstants.Params.userAgent));
        // 10. shopuuid
        extParams.put("shopUuid", activityContext.getParam(ShelfActivityConstants.Params.shopUuid));
        // 11. unionId
        extParams.put("unionId", activityContext.getParam(ShelfActivityConstants.Params.unionId));
        // 12. osType
        extParams.put("osType", getOsType(activityContext));
        // 13. appId
        extParams.put("appId", activityContext.getParam(ShelfActivityConstants.Params.appId));
        // 14. openId
        extParams.put("openId", activityContext.getParam(ShelfActivityConstants.Params.openId));


        // 以下非标准字段跟着主题走
        extParams.put("nailexhibitid", activityContext.getParam(ShelfActivityConstants.Params.nailexhibitid));
        extParams.put("shelfNavTagId", activityContext.getParam(ShelfActivityConstants.Params.selectedFilterId));
        extParams.put("dealId2ShopId", buildDealId2ShopId(products, PlatformUtil.isMT(platform) ? (int) mtShopId : (int) dpShopId));
        extParams.put("dealId2ShopIdForLong", buildDealId2ShopIdForLong(products, PlatformUtil.isMT(platform) ? mtShopId : dpShopId));
        extParams.put("displayControlSceneType", ParamsUtil.getIntSafely(params, "displayControlSceneType"));

        extParams.put("attributeKeys", params.get("attributeKeys"));
        extParams.put("dealAttributeKeys", params.get("dealAttributeKeys"));
        extParams.put("contentSubBizTypeList", params.get("contentSubBizTypeList"));
        extParams.put("fetchContentDetailTag", params.get("fetchContentDetailTag"));
        extParams.put("promoTemplateId", params.get("promoTemplateId"));
        extParams.put("noMergePromoTemplateId", params.get("noMergePromoTemplateId"));
        extParams.put("tcMergePromoTemplateId", params.get("tcMergePromoTemplateId"));
        extParams.put("shelfNavSortOrder", params.get("shelfNavSortOrder"));
        extParams.put("shelfNavSortType", params.get("shelfNavSortType"));
        extParams.put("shelfNavPageNo", params.get("shelfNavPageNo"));
        extParams.put("shelfNavPageSize", params.get("shelfNavPageSize"));
        extParams.put("shopId2UuidMap", params.get("shopId2UuidMap") == null ? activityContext.getParam("shopId2UuidMap") : params.get("shopId2UuidMap"));
        extParams.put("shopIdLong2UuidMap", params.get("shopIdLong2UuidMap") == null ? activityContext.getParam("shopIdLong2UuidMap") : params.get("shopIdLong2UuidMap"));
        extParams.put("scene", params.get("scene"));
        extParams.put("directPromoSceneCode", directPromoSceneCode);
        extParams.put("shopMainCategoryId", params.get("shopMainCategoryId"));
        extParams.put("goodsType", params.get("goodsType"));
        extParams.put("guaranteeTypes", params.get("guaranteeTypes"));
        addPromoCodeExtraInfo(extParams, activityContext, params);

        extParams.put("activityCategoryKey", params.get("activityCategoryKey"));
        extParams.put("activityIdKey", params.get("activityIdKey"));
        extParams.put("activityFloorIds", params.get("activityFloorIds"));
        extParams.put("shopThemePlanId", params.get("shopThemePlanId"));
        extParams.put("countrywideShop", params.get("countrywideShop"));
        extParams.put("dealId2NearestShopId", activityContext.getParam("dealId2NearestShopId"));
        extParams.put("dealId2NearestShopIdL", activityContext.getParam("dealId2NearestShopIdL"));
        extParams.put("keyword", activityContext.getParam(ShelfActivityConstants.Params.keyword));
        extParams.put("shopCategory", getShopCategory(params, activityContext));
        extParams.put("bindExhibitBizType", getBindExhibitBizType(activityContext, params));
        extParams.put("expSks", buildExpSks(activityContext));
        extParams.put("mtSIFlag", activityContext.getParam(ShelfActivityConstants.Params.mtSIFlag));
        //主题请求来源，后续可能细分列表、货架，当前默认传货架即可
        extParams.put("themeReqSource", ThemeReqSourceEnum.POI_SHELF.getType());
        extParams.put("pageSource", getPageSource(activityContext, params));
        extParams.put("riskParam", ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.riskParam));
        extParams.put("querySceneStrategy", getSecSkillSceneStrategy(params));
        extParams.put("activityId", activityContext.getParam("activityId"));
        extParams.put("enablePreSalePromoTag", params.get("enablePreSalePromoTag"));
        extParams.put("priceDescType", this.getPriceDescType(activityContext, productGroupM, params));
        extParams.put("bpDealGroupTypes", params.get("bpDealGroupTypes"));
        extParams.put("platformShelfSceneCode", getPlatformSceneCode(activityContext, params));
        extParams.put("spuSceneTypes", params.get("spuSceneTypes"));
        extParams.put(PaddingFetcher.Params.ACTIVITY_SOURCE, params.get(PaddingFetcher.Params.ACTIVITY_SOURCE));

        String channelType = ParamsUtil.getStringSafely(activityContext.getParameters(), ShelfActivityConstants.Params.channelType);
        extParams.put("channelType", channelType);
        extParams.put("offlineCode", ParamsUtil.getParamFromExtraMap(activityContext.getParameters(), "offlinecode", ""));

        // lpy修改 dealProductSource
        appendDealProductSourceInfo(extParams,params,channelType,activityContext);
        // 参考 com.sankuai.dealtheme.core.enums.ShelfTypeEnum
        extParams.put("dealShelfStyleType", getDealShelfStyleType(activityContext, productGroupM, params));
        //商场参数处理
        if (MallUtils.isMallScene(activityContext.getSceneCode())) {
            handleMallSceneExtParams(activityContext, extParams);
        }
        //家居进场零售参数处理
        if (HomeStoreUtils.isHomeStoreScene(activityContext.getSceneCode())) {
            handleHomeStoreSceneExtParams(extParams, products, PlatformUtil.isMT(platform) ? mtShopId : dpShopId);
        }
        addConfigExtraParams(params, extParams);
        addPriceSecretInfo(activityContext, extParams);
        addChannelParam(activityContext, extParams);
        //商家会员价参数处理
        extParams.put("enableMerchantMemberPromo",params.get("enableMerchantMemberPromo"));
        //神会员用价格来源
        if(needPricePageSource(activityContext)){
            extParams.put("pricePosition", ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.position));
            extParams.put("pricePageSource", PageSourceEnum.poiDetailPage.getType());
        }
        addDealMaterial(extParams, products);

        //游戏厅游戏币筛选项信息
        if(isGameCoinScene(activityContext)){
            extParams.put("gameCoinFilterId",params.get("gameCoinFilterId"));
        }
        // 加项相关配置
        extParams.put("additionalAttrKeys", params.get("additionalAttrKeys"));
        extParams.put("combineType", params.get("combineType"));
        //优惠明细类型
        extParams.put("promoDetailTemplate", getPromoDetailTemplate(activityContext));
        //曝光活动场景
        extParams.put("activityQueryScene", getActivityQueryScene(activityContext));
        //屏幕高度，提前获取团详布局
        Integer deviceHeight = activityContext.getParam("deviceHeight");
        if (deviceHeight != null && deviceHeight > 0) {
            extParams.put("deviceHeight", deviceHeight);
        }
        // 追加运营配置化的配置
        appendOperatorConfigs(activityContext, extParams);
        //商品的最低价格skuid
        extParams.put("dealId2SkuId",buildDealId2SkuId(products));
        //团单关联sku
        extParams.put("dealId2SkuIdList", buildDealId2Skus(products));
        //sku属性
        extParams.put("skuAttributeKeys", params.get("skuAttributeKeys"));
        return extParams;
    }

    /**
     * 往extParams添加神券标识参数，mmcInflate：神券膨胀标识；mmcUse：神券可用标识；mmcBuy:券包可买标识；mmcFree：神券可领塞标识
     * 货架不需要添加神券标识参数，线下码渠道进货架需要神券标识（渠道可扩展）
     */
    private void appendDealProductSourceInfo(Map<String, Object> extParams, Map<String, Object> params,
            String channelType, ActivityContext activityContext) {
        String dealProductSource = getDealProductSource(channelType, params);
        if (LionObjectManagerUtils.isNeedMmcParams(dealProductSource)) {
            extParams.put("mmcInflate",
                    ParamsUtil.getParamFromExtraMap(activityContext.getParameters(), "mmcinflate", ""));
            extParams.put("mmcUse", ParamsUtil.getParamFromExtraMap(activityContext.getParameters(), "mmcuse", ""));
            extParams.put("mmcBuy", ParamsUtil.getParamFromExtraMap(activityContext.getParameters(), "mmcbuy", ""));
            extParams.put("mmcFree", ParamsUtil.getParamFromExtraMap(activityContext.getParameters(), "mmcfree", ""));
        }
        extParams.put(PaddingFetcher.Params.DEAL_PRODUCT_SOURCE, dealProductSource);
    }

    private String getDealProductSource(String channelType, Map<String, Object> params) {
        return LionObjectManagerUtils.isValidChannelType(channelType) ? channelType
                : ParamsUtil.getStringSafely(params, PaddingFetcher.Params.DEAL_PRODUCT_SOURCE);
    }

    private void appendOperatorConfigs(ActivityContext activityContext, Map<String, Object> extParams) {
        try {
            Set<String> appendAttrs = UnifiedShelfOperatorConfigUtils.getOperatorConfigAttrs(activityContext);
            if (CollectionUtils.isEmpty(appendAttrs)) {
                return;
            }
            List<String> currentAttributeKeys = (List<String>) extParams.get("attributeKeys");
            // 保持原有顺序
            appendAttrs.stream().forEach(attr -> {
                if (currentAttributeKeys.contains(attr)) {
                    return;
                }
                currentAttributeKeys.add(attr);
            });
            // 因为依赖service_type_leaf_id,所以直接添加
            if (!currentAttributeKeys.contains(SERVICE_TYPE_LEAF_ID)) {
                currentAttributeKeys.add(SERVICE_TYPE_LEAF_ID);
            }
            // 有探针属性
            extParams.put("hasProductProbeAttr", true);
        } catch (Exception e) {
            log.error("appendOperatorConfigsError，ctxParam={}", JsonCodec.encodeWithUTF8(activityContext.getParameters()), e);
            Cat.logError("appendAIGrayConfigAttrKeysError", e);
        }
    }

    private int getOsType(ActivityContext activityContext) {
        String osTypeStr = activityContext.getParam(ShelfActivityConstants.Params.clientType);
        if (ANDROID.equals(osTypeStr)) {
            return OS_TYPE_ANDROID_INT;
        }
        if (HARMONY.equalsIgnoreCase(osTypeStr)) {
            return OS_TYPE_HARMONY_INT;
        }
        return OS_TYPE_IOS_INT;
    }

    private void addPromoCodeExtraInfo(Map<String, Object> extParams, ActivityContext activityContext, Map<String, Object> params) {
        if (PromoCodeUtils.checkShopAutoVerify(activityContext)) {
            params.put("bpDealGroupTypes", activityContext.getParam("bpDealGroupTypes"));
        }
        if (activityContext.getParam(ShelfActivityConstants.Params.promoCodeExtraInfo) == null) {
            return;
        }
        extParams.put(ShelfActivityConstants.Params.orderTrafficFlag, activityContext.getParam(ShelfActivityConstants.Params.orderTrafficFlag));
        Map<String, Object> extraMap = JsonCodec.decode((String) activityContext.getParam(ShelfActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {
        });
        extParams.put("codeType", getScanCodeType(extraMap, activityContext));
        extParams.put("staffId2techId", buildStaffId2TechIdMap(extraMap, activityContext));
        extParams.put("promoCodeExtraInfo", activityContext.getParam(ShelfActivityConstants.Params.promoCodeExtraInfo));
    }

    private Map<Long, Integer> buildStaffId2TechIdMap(Map<String, Object> extraMap, ActivityContext activityContext) {
        if (getScanCodeType(extraMap, activityContext) == 3
                && activityContext.getParam(ShelfActivityConstants.Params.entityId) != null) {
            Integer techId = (Integer) extraMap.get("techId");
            Map<Long, Integer> staffId2TechIdMap = new HashMap<>(1);
            staffId2TechIdMap.put(Long.valueOf(activityContext.getParam(ShelfActivityConstants.Params.entityId)), techId);
            return staffId2TechIdMap;
        }
        return Maps.newHashMap();
    }

    private Integer getScanCodeType(Map<String, Object> extraMap, ActivityContext activityContext) {
        if (MapUtils.isNotEmpty(extraMap) && extraMap.containsKey("codeType")) { //优先从参数获取
            return IntegerUtil.parseInt(extraMap.get("codeType"), 0);
        } else if ("activity_beauty_medical_coupon_staff_nav_shelf".equals(activityContext.getSceneCode())) {
            return QRCodeType.PROMO_CODE_EMPLOYEE.getCode();
        }
        return 0; //其他
    }

    private boolean needPricePageSource(ActivityContext activityContext){
        String spaceKey = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.spaceKey);
        //货架场景必传
        if(DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY.equals(spaceKey)){
            return true;
        }
        //货架场景必传
        if (UnifiedShelfActivityCtxBuilder.UNIFIED_SHELF_SPACE_KEY.equals(spaceKey)){
            return true;
        }
        //其他场景按白名单，key为spaceKey, value为scene列表
        if(MapUtils.isEmpty(pricePageSourceSceneWhiteList)){
            return false;
        }
        List<String> sceneCodeList= pricePageSourceSceneWhiteList.get(spaceKey);
        if(CollectionUtils.isEmpty(sceneCodeList)){
            return false;
        }
        String sceneCode = activityContext.getSceneCode();
        return sceneCodeList.contains(sceneCode);
    }

    private Boolean isGameCoinScene(ActivityContext activityContext){
        if(activityContext.getSceneCode().equals(GAME_COIN_SCENE_CODE)){
            return true;
        }
        return false;

    }

    private void addDealMaterial(Map<String, Object> extParams, List<ProductM> products) {
        Map<Integer, List<ItemUnitM>> itemUnitMap = products.stream()
                .filter(t -> CollectionUtils.isNotEmpty(t.getItemUnitList()))
                .flatMap(t -> t.getItemUnitList().stream().map(itemUnit -> new AbstractMap.SimpleEntry<>(t.getProductId(), itemUnit)))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())
                ));
        if (MapUtils.isEmpty(itemUnitMap)) {
            return;
        }
        Map<Integer, List<DealMaterialReq>> dealId2Material = new HashMap<>(itemUnitMap.size());
        for (Map.Entry<Integer, List<ItemUnitM>> itemUnitEntry : itemUnitMap.entrySet()) {
             List<DealMaterialReq> materialReqList = itemUnitEntry.getValue().stream().filter(t->t.getItemUnitType() == ShelfItemUnitTypeEnum.MANICURE_STYLE.getCode()).map(t->{
                DealMaterialReq dealMaterialReq = new DealMaterialReq();
                dealMaterialReq.setMaterialId(t.getItemUnitId());
                // 穿戴甲
                dealMaterialReq.setSearchScene("search_press_on_manicure_style_by_info_id");
                dealMaterialReq.setSaleType(4);
                return dealMaterialReq;
            }).collect(Collectors.toList());
             if(CollectionUtils.isNotEmpty(materialReqList)){
                 dealId2Material.put(itemUnitEntry.getKey(), materialReqList);
             }
        }
        if(MapUtils.isNotEmpty(dealId2Material)){
            extParams.put("dealId2Material", dealId2Material);
        }
    }

    private void addConfigExtraParams(Map<String, Object> params, Map<String, Object> extParams) {
        if (params.get("configExtraParams") == null) {
            return;
        }
        Map<String, Object> configExtraParams = (Map<String, Object>) params.get("configExtraParams");
        extParams.putAll(configExtraParams);
    }

    private void addChannelParam(ActivityContext activityContext, Map<String, Object> extParams) {
        //华为渠道
        String pageSource = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.pageSource);
        if(StringUtils.isNotEmpty(pageSource) && pageSource.contains("huaweifuyiping")){
            extParams.put("dealProductSource", ConstantsSourceEnum.HUA_WEI.getCode());
            return;
        }
        //百度地图渠道
        int userAgent = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.userAgent);
        if(userAgent == VCClientTypeEnum.DP_BAIDU_MAP_XCX.getCode() || userAgent == 101100){
            extParams.put("dealProductSource", ConstantsSourceEnum.BAIDU_MAP.getCode());
            extParams.put("pageSource", "source=baiduMap");
            return;
        }
    }

    private void addPriceSecretInfo(ActivityContext activityContext, Map<String, Object> extParams){
        //价格一致率加密字符串
        extParams.put("priceSecretInfo", activityContext.getParam(ShelfActivityConstants.Params.pricecipher));
        //页面名称，用于计算价格一致率
        extParams.put("pageName", PageNameEnum.DealNormalShelf.getCode());
    }

    private void handleMallSceneExtParams(ActivityContext activityContext, Map<String, Object> extParams) {
        Object dealId2ShopId = activityContext.getParam(ShelfActivityConstants.Params.dealId2ShopId);
        Object dealId2ShopIdForLong = activityContext.getParam(ShelfActivityConstants.Params.dealId2ShopIdForLong);
        if (Objects.nonNull(dealId2ShopId)) {
            extParams.put("dealId2ShopId", dealId2ShopId);
        }
        if (Objects.nonNull(dealId2ShopIdForLong)) {
            extParams.put("dealId2ShopIdForLong", dealId2ShopIdForLong);
        }
    }

    private void handleHomeStoreSceneExtParams(Map<String, Object> extParams, List<ProductM> products, long shopId) {
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        Map<Integer, Integer> dealId2ShopIdMap =  products.stream().collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), getHomeStoreShopId((int) shopId, productM)), HashMap::putAll);
        Map<Integer, Long> dealId2ShopIdMapForLong =  products.stream().collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), getHomeStoreShopIdForLong(shopId, productM)), HashMap::putAll);
        extParams.put("dealId2ShopId", dealId2ShopIdMap);
        extParams.put("dealId2ShopIdForLong", dealId2ShopIdMapForLong);
    }

    /**
     * 获取团购货架样式类型
     *
     * @param activityContext
     * @param productGroupM
     * @return
     */
    private Object getDealShelfStyleType(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        if (Objects.nonNull(params.get("dealShelfStyleType"))) {
            return params.get("dealShelfStyleType");
        }
        if (Objects.nonNull(params.get("dealShelfStyleTypeByProductNum"))) {
            int totalProductNum = getTotalProductNum(activityContext, productGroupM);
            int minNum = (int) params.get("dealShelfStyleTypeByProductNum");
            if (totalProductNum < minNum) {
                // 单列小图
                return 1;
            } else {
                // 双列小图
                return 3;
            }
        }
        return null;
    }

    private Integer getPriceDescType(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        if (Objects.isNull(params.get("totalProductNumToPriceDesType"))) {
            if (Objects.isNull(params.get("priceDescType"))) {
                return getExpPriceDescType(activityContext, productGroupM, params);
            } else {
                return (int) params.get("priceDescType");
            }
        }
        // 若 totalProductNumToPriceDesType 不为null + priceDescType 为null，则根据 商品数量来判断单双列
        int minNum = (int) params.get("totalProductNumToPriceDesType");
        // 计算商品总数
        int totalProductNum = getTotalProductNum(activityContext, productGroupM);

        if (totalProductNum >= minNum) {
            return PriceDescEnum.DoubleShelf.getType();
        } else {
            return PriceDescEnum.NEW_SingleShelf.getType();
        }
    }

    private int getTotalProductNum(ActivityContext activityContext, ProductGroupM productGroupM) {
        // 计算商品总数
        int totalProductNum;
        if (StringUtils.isNotEmpty(activityContext.getParam("extra")) && activityContext.getParam("extra").toString().contains("totalProductNum")) {
            // 前端传了extra,取Extra
            JsonNode extraJson = JsonUtils.readJsonNode(activityContext.getParam("extra"));
            totalProductNum = extraJson.findValue("totalProductNum").intValue();
        } else {
            // 若没有extra，则为首屏请求，根据召回的数量来判断
            totalProductNum = productGroupM.getTotal();
        }
        return totalProductNum;
    }


    private Integer getExpPriceDescType(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        List<String> expList = buildExpSks(activityContext);
        if (MapUtils.isNotEmpty(params) && Objects.nonNull(params.get(PaddingFetcher.Params.expToPriceDescTypeMap)) && CollectionUtils.isNotEmpty(expList)) {
            Cat.logEvent("ExpHarnessing", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.DealGroupThemeHandler.getExpPriceDescType");
            Map<String, Integer> expToPriceDescTypeMap = (Map<String, Integer>) params.get(PaddingFetcher.Params.expToPriceDescTypeMap);
            for (String exp : expList) {
                if (MapUtils.isNotEmpty(expToPriceDescTypeMap) && expToPriceDescTypeMap.containsKey(exp)) {
                    return expToPriceDescTypeMap.get(exp);
                }
            }
        }
        return null;
    }

    public static List<String> buildExpSks(ActivityContext activityContext) {
        List<DouHuM> douHuMList = activityContext.getParam(ShelfActivityConstants.Params.douHus);
        if (CollectionUtils.isEmpty(douHuMList)) {
            return new ArrayList<>();
        }
        return douHuMList.stream().filter(douHuM -> douHuM != null && StringUtils.isNotEmpty(douHuM.getSk())).map(DouHuM::getSk).collect(Collectors.toList());
    }

    private Integer getShopCategory(Map<String, Object> params, ActivityContext activityContext) {
        //优先指定
        Integer categoryInParam = (Integer) params.get(PaddingFetcher.Params.shopCategory);
        if (categoryInParam != null && categoryInParam > 0) {
            return categoryInParam;
        }
        ShopM shopM = activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return 0;
        }
        return shopM.getCategory();
    }

    /**
     * @param activityContext
     * @return 枚举{@link com.sankuai.mpmctexhibit.common.enums.BizIdEnum} 0是无效值，主题层做过滤
     */
    private int getBindExhibitBizType(ActivityContext activityContext, Map<String, Object> groupParams) {
        //优先取内部参数指定值，再取外部传入值
        int type = ParamsUtil.getIntSafely(groupParams, "bindExhibitBizType");
        if (type < 1) {
            type = ParamsUtil.getIntSafely(activityContext, FilterListActivityConstants.Params.categoryId);
        }
        return type;
    }

    private Integer getDirectPromoSceneCode(Map<String, Object> params, List<String> expList) {
        if (MapUtils.isNotEmpty(params) && Objects.nonNull(params.get(PaddingFetcher.Params.expToDirectPromoSceneMap)) && CollectionUtils.isNotEmpty(expList)) {
            Map<String, Integer> expToDirectPromoSceneMap = (Map<String, Integer>) params.get(PaddingFetcher.Params.expToDirectPromoSceneMap);
            for (String exp : expList) {
                if (MapUtils.isNotEmpty(expToDirectPromoSceneMap) && expToDirectPromoSceneMap.containsKey(exp)) {
                    return expToDirectPromoSceneMap.get(exp);
                }
            }
        }
        if (params.get(PaddingFetcher.Params.directPromoSceneCode) == null) {
            return PaddingFetcher.PriceSceneType.SHELF_DIRECT_PROMO.code;
        }
        return (Integer) params.get(PaddingFetcher.Params.directPromoSceneCode);
    }

    public Map<Integer, Integer> buildDealId2ShopId(List<ProductM> products, int shopId) {
        if (CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }
        return products.stream().collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), (int)getShopId(shopId, productM)), HashMap::putAll);
    }

    public Map<Integer, Long> buildDealId2ShopIdForLong(List<ProductM> products, long shopId) {
        if (CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }
        return products.stream().collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), getShopId(shopId, productM)), HashMap::putAll);
    }

    public Map<Integer, Integer> buildDealId2SkuId(List<ProductM> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }

        return products.stream()
                .filter(this::isValidLowPriceSkuId)
                .collect(Collectors.toMap(
                        ProductM::getProductId,
                        productM -> productM.getLowPriceSkuId().intValue(),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    public Map<Integer, List<Long>> buildDealId2Skus(List<ProductM> products) {
        if (CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }

        return products.stream()
                .filter(productM -> CollectionUtils.isNotEmpty(productM.getSkuIdList()))
                .collect(Collectors.toMap(
                        ProductM::getProductId,
                        productM -> productM.getSkuIdList().stream().map(Long::parseLong).collect(Collectors.toList()),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    private boolean isValidLowPriceSkuId(ProductM productM) {
        return Objects.nonNull(productM.getLowPriceSkuId()) && productM.getLowPriceSkuId() > 0;
    }

    private long getShopId(long shopId, ProductM productM) {
        if (shopId > 0) {
            return shopId;
        }
        if (productM == null || CollectionUtils.isEmpty(productM.getShopIds())) {
            return shopId;
        }
        return PoiIdUtil.getShopIdsL(productM).get(0);
    }

    private int getHomeStoreShopId(int shopId, ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getShopIds())) {
            return shopId;
        }
        return productM.getShopIds().get(0);
    }

    private long getHomeStoreShopIdForLong(long shopId, ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(PoiIdUtil.getShopIdsL(productM))) {
            return shopId;
        }

        return PoiIdUtil.getShopIdsL(productM).get(0);
    }

    private long getUserId(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        long dpUserId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        long mtUserId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtUserId);
        if (PlatformUtil.isMT(platform)) {
            return mtUserId;
        }
        return dpUserId;
    }

    private String getPromoDetailTemplate(ActivityContext activityContext) {
        String promoDetailTemplate = PromoDetailTemplateEnum.OldVersion.getCode();
        if (PromoSimplifyUtils.hitPromoSimplifyV2(activityContext)) {
            promoDetailTemplate = PromoDetailTemplateEnum.NewVersion.getCode();
        }
        if (MagicalMemberTagUtils.hitExp(activityContext)) {
            return new StringBuilder()
                    .append(promoDetailTemplate)
                    .append(",")
                    .append(PromoDetailTemplateEnum.EnhancePerception.getCode()).toString();
        }
        return promoDetailTemplate;
    }

    private String getActivityQueryScene(ActivityContext activityContext) {
        //1-旧曝光活动类型，2-新曝光活动类型，见ExposureExtParamKeyEnum.QUERY_SCENE
        return PromoSimplifyUtils.hitPromoSimplifyV2(activityContext) ? "2" : "1";
    }

    private Object getSecSkillSceneStrategy(Map<String, Object> params){
        if(params.get("querySecKillSceneStrategy") == null){
            return QuerySecKillSceneStrategyEnum.VALID_filterNoReduce.getCode();
        }
        return params.get("querySecKillSceneStrategy");
    }

    private Object getPlatformSceneCode(ActivityContext activityContext, Map<String, Object> params) {
        String platformSceneCode = activityContext.getParam(QueryFetcher.Params.platformSceneCode);
        if(StringUtils.isNotEmpty(platformSceneCode)){
            return platformSceneCode;
        }
        return params.get("platformShelfSceneCode");
    }

    private UseRuleM buildUseRule(DealGroupUseRuleDTO useRule) {
        if (useRule == null){
            return null;
        }
        UseRuleM useRuleM = new UseRuleM();
        useRuleM.setAvailableDate(toAvailableDateM(useRule.getAvailableDate()));
        useRuleM.setDisableDate(toDisableDateM(useRule.getDisableDate()));
        return useRuleM;
    }

    private List<DealProductMaterialM> buildMaterialList(List<DealProductMaterialDTO> materialList, ProductM productM) {
        if (CollectionUtils.isEmpty(materialList) || CollectionUtils.isEmpty(productM.getItemUnitList())) {
            return Collections.emptyList();
        }
        Set<String> itemUnitIdSet = productM.getItemUnitList().stream().map(ItemUnitM::getItemUnitId).collect(Collectors.toSet());
        return materialList.stream().filter(t -> itemUnitIdSet.contains(t.getMaterialId())).map(t -> {
            DealProductMaterialM materialM = new DealProductMaterialM();
            BeanUtils.copyProperties(t, materialM);
            return materialM;
        }).collect(Collectors.toList());
    }

    private AvailableDateM toAvailableDateM(AvailableDateDTO availableDate) {
        if (availableDate == null){
            return null;
        }
        AvailableDateM dateM = new AvailableDateM();
        dateM.setAvailableType(availableDate.getAvailableType());
        dateM.setCycleAvailableDateList(toCycleAvailableDateList(availableDate.getCycleAvailableDateList()));
        dateM.setSpecifiedDurationDateList(toSpecifiedDurationDateList(availableDate.getSpecifiedDurationDateList()));
        return dateM;
    }

    private DisableDateM toDisableDateM(DisableDateDTO disableDate) {
        if (disableDate == null){
            return null;
        }
        DisableDateM dateM = new DisableDateM();
        dateM.setDisableDays(disableDate.getDisableDays());
        dateM.setDisableDateRangeDTOS(toDateRangeM(disableDate.getDisableDateRangeDTOS()));
        return dateM;
    }

    private List<CycleAvailableDateM> toCycleAvailableDateList(List<CycleAvailableDateDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(Objects::nonNull).map(e -> {
            CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
            cycleAvailableDateM.setAvailableDays(e.getAvailableDays());
            cycleAvailableDateM.setAvailableTimeRangePerDay(toDateRangeM(e.getAvailableTimeRangePerDay()));
            return cycleAvailableDateM;
        }).collect(Collectors.toList());
    }

    private List<AvailableDurationDateM> toSpecifiedDurationDateList(List<AvailableDurationDateDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(Objects::nonNull).map(e -> {
            AvailableDurationDateM availableDurationDateM = new AvailableDurationDateM();
            availableDurationDateM.setAvailableDateRangeDTOS(toDateRangeM(e.getAvailableDateRangeDTOS()));
            availableDurationDateM.setAvailableTimeRangePerDay(toDateRangeM(e.getAvailableTimeRangePerDay()));
            return availableDurationDateM;
        }).collect(Collectors.toList());
    }

    private List<DateRangeM> toDateRangeM(List<DateRangeDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().filter(Objects::nonNull).map(e -> {
            DateRangeM dateRangeM = new DateRangeM();
            dateRangeM.setFrom(e.getFrom());
            dateRangeM.setTo(e.getTo());
            return dateRangeM;
        }).collect(Collectors.toList());
    }

    private void putMagicalMemberDyeContext(ProductGroupM productGroupM, ActivityContext ctx){
        if(CollectionUtils.isEmpty(productGroupM.getProducts())){
            return;
        }
        try {
            boolean isMagicalTrace = productGroupM.getProducts().stream()
                    .filter(Objects::nonNull)
                    .map(ProductM::getPromoPrices)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .anyMatch(DzPromoUtils::promoCombinationWithMagicalMemberCoupon);
            if (isMagicalTrace) {
                SptDyeUtil.DyeTraceParam traceParam = SptDyeUtil.DyeTraceParam.ofFlowEntrance("MAGIC_MEMBER", "default");
                SptDyeUtil.putDyeTraceParam(traceParam);
                //城市染色
                int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
                String locateCityId = ParamsUtil.getStringSafely(ctx, ShelfActivityConstants.Params.locationCityId);
                SptDyeUtil.DyeTraceParam magicMemberCityParam = PlatformUtil.isMT(platform) ? SptDyeUtil.DyeTraceParam.ofMagicMemberMtCity(locateCityId) : SptDyeUtil.DyeTraceParam.ofMagicMemberDpCity(locateCityId);
                SptDyeUtil.putDyeTraceParam(magicMemberCityParam);

                MagicalMemberMonitorUtils.markTrace();
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    public String getPageSource(ActivityContext context, Map<String, Object> params) {
        String pageSource = ParamsUtil.getStringSafely(context, ShelfActivityConstants.Params.pageSource);
        if (StringUtils.isNotEmpty(pageSource)) {
            return appendPassParam(context, pageSource);
        }
        pageSource = String.valueOf(params.getOrDefault(ShelfActivityConstants.Params.pageSource, ""));
        if (StringUtils.isNotEmpty(pageSource)) {
            return appendPassParam(context, pageSource);
        }
        return buildPassParam(context);
    }

    private String appendPassParam(ActivityContext context,String pageSource) {
        String passParam = buildPassParam(context);
        if (StringUtils.isNotEmpty(passParam)) {
            StringBuilder urlWithPageSource = new StringBuilder();
            urlWithPageSource.append(pageSource);
            urlWithPageSource.append("&");
            urlWithPageSource.append(passParam);
            return urlWithPageSource.toString();
        }
        return pageSource;
    }

    private String buildPassParam(ActivityContext context) {
        try {
            //订单来源新增品牌旗舰店
            if (Objects.equals(DealFilterListActivity.SpaceKey.FLAGSHIP_STORE_SHELF, context.getParam(ShelfActivityConstants.Params.spaceKey))) {
                Map<String,Object> distributionBasicInfo = new HashMap<>();
                distributionBasicInfo.put("distributionType","FLAGSHIP_POI_PAGE");//主渠道
                distributionBasicInfo.put("sceneId","GoodsShelf");//子渠道
                List<Map<String,Object>> distributionBasicInfoList = new ArrayList<>();
                distributionBasicInfoList.add(distributionBasicInfo);
                Map<String,Object> distribution = new HashMap<>();
                distribution.put("distributionInfoList",distributionBasicInfoList);
                Map<String,Object> passParam = new HashMap<>();
                passParam.put("DISTRIBUTION_BASIC_INFO",new Gson().toJson(distribution));
                return  "pass_param="+ URLEncoder.encode(new Gson().toJson(passParam),"utf-8");
            }
        } catch (Exception e) {
            log.error("DealGroupThemeHandler#buildPassParam.error",e);
        }
        return null;
    }
}
