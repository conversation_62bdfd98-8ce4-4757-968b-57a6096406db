package com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product;

import cn.hutool.core.bean.BeanUtil;
import com.dianping.cat.Cat;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.util.Map;

/**
 *  用于根据指定属性名获取商户Boolean类型的属性值
 *
 * Created by float.lu on 2020/11/7.
 */
public class ShopBooleanFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product.ShopBooleanFunction.call(java.util.Map,com.googlecode.aviator.runtime.type.AviatorObject)");
        ProductM productM = (ProductM) env.get("item");
        if (productM == null || CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return AviatorRuntimeJavaType.valueOf(false);
        }
        String fieldName = FunctionUtils.getStringValue(arg1, env);
        if (StringUtils.isBlank(fieldName)) {
            return AviatorRuntimeJavaType.valueOf(false);
        }
        Object value = BeanUtil.getProperty(productM.getShopMs().get(0), fieldName);
        if (value == null || Boolean.FALSE.toString().equals(value.toString())) {
            return AviatorRuntimeJavaType.valueOf(false);
        }
        return AviatorRuntimeJavaType.valueOf(true);
    }

    @Override
    public String getName() {
        return "shopBoolean";
    }
}
