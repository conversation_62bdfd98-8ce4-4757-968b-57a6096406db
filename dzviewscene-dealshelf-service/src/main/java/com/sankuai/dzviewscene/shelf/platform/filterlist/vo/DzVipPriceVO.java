package com.sankuai.dzviewscene.shelf.platform.filterlist.vo;

import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.enums.VipPriceType;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员价
 * @auther: liweilong06
 * @date: 2020/9/17 2:25 下午
 */
@Data
public class DzVipPriceVO implements Serializable {

    /**
     * 1:会员价，2：会员日价，3：玩乐卡
     * {@link VipPriceType}
     */
    private int vipPriceType;

    /**
     * 用户是否持有会员卡
     */
    private boolean userHasMemberCard;

    /**
     * 价格
     */
    private String price;

    /**
     * 价格描述，如"起"
     */
    private String priceDesc;

    /**
     * 标签图片
     */
    private String tagImg;

}
