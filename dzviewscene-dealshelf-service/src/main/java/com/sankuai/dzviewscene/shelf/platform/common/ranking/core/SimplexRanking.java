package com.sankuai.dzviewscene.shelf.platform.common.ranking.core;

import com.sankuai.dzviewscene.shelf.platform.common.ranking.Ranking;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.TransformerExecutor;

import java.util.List;

/**
 * 单路召回, 从召回池中召回一批商品, 不改变上下文
 *
 * Created by float.lu on 2020/11/6.
 */
public class SimplexRanking implements Ranking {

    private List<Transformer> transformers;

    public SimplexRanking(List<Transformer> transformers) {
        this.transformers = transformers;
    }

    @Override
    public List<RankingItem> rank(RankingContext rankingContext) {
        List<RankingItem> rankingResult = rankingContext.getPool();
        for (Transformer transformer : transformers) {
            rankingResult = TransformerExecutor.executeSafely(transformer, rankingContext, rankingResult);
        }
        return rankingResult;
    }
}
