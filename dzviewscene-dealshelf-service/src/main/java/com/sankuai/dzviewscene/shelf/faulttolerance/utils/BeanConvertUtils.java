package com.sankuai.dzviewscene.shelf.faulttolerance.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhangsuping on 2021/12/31.
 */
public class BeanConvertUtils {

    /**
     * 构造DzShelfResponseVO对象
     * @param shelfResponseVO
     * @return
     */
    public static DzShelfResponseVO buildDzShelfResponseVO(DzShelfResponseVO shelfResponseVO) {
        try {
            if (inValidResult(shelfResponseVO)) {
                return null;
            }
            DzShelfComponentVO dzShelfComponentVO = new DzShelfComponentVO();
            dzShelfComponentVO.setCategoryId(shelfResponseVO.getShelfComponent().getCategoryId());
            dzShelfComponentVO.setMainTitle(buildMainTitleComponentVO(shelfResponseVO.getShelfComponent().getMainTitle()));
            dzShelfComponentVO.setFilter(buildFilterComponentVO(shelfResponseVO.getShelfComponent().getFilter()));
            dzShelfComponentVO.setFilterIdAndProductAreas(buildFilterBtnIdAndProAreas(shelfResponseVO.getShelfComponent().getFilterIdAndProductAreas()));
            dzShelfComponentVO.setShowType(shelfResponseVO.getShelfComponent().getShowType());
            dzShelfComponentVO.setSceneCode(shelfResponseVO.getShelfComponent().getSceneCode());
            DzShelfResponseVO currentShelfComponentVO = new DzShelfResponseVO();
            currentShelfComponentVO.setShelfComponent(dzShelfComponentVO);
            currentShelfComponentVO.setOcean(buildOcean());
            return currentShelfComponentVO;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    /**
     * 构造FilterBtnIdAndProAreasVO对象
     * @param filterBtnIdAndProAreasVO
     * @return
     */
    public static FilterBtnIdAndProAreasVO buildFilterIdAndProductArea(FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO) {
        try {
            if (invalidFilterBtnIdAndProAreasVO(filterBtnIdAndProAreasVO)) {
                return null;
            }
            FilterBtnIdAndProAreasVO currentFilterBtnIdAndProAreas = new FilterBtnIdAndProAreasVO();
            currentFilterBtnIdAndProAreas.setFilterBtnId(filterBtnIdAndProAreasVO.getFilterBtnId());
            currentFilterBtnIdAndProAreas.setSceneCode(filterBtnIdAndProAreasVO.getSceneCode());
            currentFilterBtnIdAndProAreas.setProductAreas(filterBtnIdAndProAreasVO.getProductAreas().stream()
                    .map(productArea -> {
                        DzItemAreaComponentVO dzItemAreaComponentVO = new DzItemAreaComponentVO();
                        dzItemAreaComponentVO.setDefaultShowNum(productArea.getItemArea().getDefaultShowNum());
                        dzItemAreaComponentVO.setHasNext(productArea.getItemArea().isHasNext());
                        dzItemAreaComponentVO.setProductItems(buildProductItems(productArea.getItemArea().getProductItems()));
                        dzItemAreaComponentVO.setShowType(productArea.getItemArea().getShowType());
                        ProductAreaComponentVO productAreaComponentVO = new ProductAreaComponentVO();
                        productAreaComponentVO.setMore(productArea.getMore());
                        productAreaComponentVO.setTitle(getTitle(productArea.getTitle()));
                        productAreaComponentVO.setItemArea(dzItemAreaComponentVO);
                        return productAreaComponentVO;
                    })
                    .collect(Collectors.toList()));
            return currentFilterBtnIdAndProAreas;
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    private static TitleComponentVO getTitle(TitleComponentVO title) {
        TitleComponentVO titleComponentVO = new TitleComponentVO();
        if (title == null) {
            return titleComponentVO;
        }
        titleComponentVO.setIcon(title.getIcon());
        titleComponentVO.setTitle(title.getTitle());
        titleComponentVO.setRichTitle(title.getRichTitle());
        titleComponentVO.setTags(title.getTags());
        return titleComponentVO;
    }

    private static List<DzItemVO> buildProductItems(List<DzItemVO> productItems) {
        List<DzItemVO> dzItemVOS = new ArrayList<>();
        productItems.forEach(
                item -> {
                    DzItemVO dzItemVO = new DzItemVO();
                    dzItemVO.setTitle(item.getTitle());
                    dzItemVO.setPic(getPic(item.getPic()));
                    dzItemVO.setSale(item.getSale());
                    dzItemVO.setRichSale(item.getRichSale());
                    dzItemVO.setSalePrice(getSalePrice(item.getBasePrice(), item.getSalePrice()));
                    dzItemVO.setProductTags(item.getProductTags());
                    dzItemVO.setSalePriceDesc(getSalePrice(item.getBasePrice(), item.getSalePriceDesc()));
                    dzItemVO.setMarketPrice(item.getMarketPrice());
                    dzItemVO.setMarketPriceDesc(item.getMarketPriceDesc());
                    dzItemVO.setJumpUrl(item.getJumpUrl());
                    dzItemVO.setItemId(item.getItemId());
                    dzItemVO.setItemIdL(item.getItemIdL());
                    dzItemVO.setAvailable(item.isAvailable());
                    dzItemVOS.add(dzItemVO);
                }
        );
        return dzItemVOS;
    }

    private static String getSalePrice(String basePrice, String salePrice) {
        //原来的这个售卖价没有数据，默认也不塞数据
        if (StringUtils.isEmpty(salePrice)) {
            return StringUtils.EMPTY;
        }
        //基础价格无效，售卖价兜底
        if (StringUtils.isEmpty(basePrice)) {
            return salePrice;
        }
        //展示基础价格（优惠之前的价格）
        return basePrice;
    }

    private static boolean inValidResult(DzShelfResponseVO shelfResponseVO) {
        return shelfResponseVO == null
                || shelfResponseVO.getShelfComponent() == null
                || CollectionUtils.isEmpty(shelfResponseVO.getShelfComponent().getFilterIdAndProductAreas());
    }

    private static ShelfOceanVO buildOcean() {
        DzShelfOceanEntryVO dzShelfOceanEntryVO = new DzShelfOceanEntryVO();
        dzShelfOceanEntryVO.setBidView("degrade_mv");
        dzShelfOceanEntryVO.setBidClick("degrade_mc");
        ShelfOceanVO shelfOceanVO = new ShelfOceanVO();
        shelfOceanVO.setProductItem(dzShelfOceanEntryVO);
        return shelfOceanVO;
    }

    private static List<FilterBtnIdAndProAreasVO> buildFilterBtnIdAndProAreas(List<FilterBtnIdAndProAreasVO> filterIdAndProductAreas) {
        return filterIdAndProductAreas.stream()
                .map(filterIdAndProductArea -> BeanConvertUtils.buildFilterIdAndProductArea(filterIdAndProductArea))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static FilterComponentVO buildFilterComponentVO(FilterComponentVO filterComponent) {
        if (filterComponent == null || CollectionUtils.isEmpty(filterComponent.getFilterBtns())) {
            return filterComponent;
        }
        List<FilterButtonVO> filterBtns = filterComponent.getFilterBtns().stream()
                .map(filterButtonVO -> buildFilterButtonVO(filterButtonVO))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterBtns)) {
            return null;
        }
        FilterComponentVO filterComponentVO = new FilterComponentVO();
        filterComponentVO.setFilterBtns(filterBtns);
        filterComponentVO.setMinShowNum(filterComponent.getMinShowNum());
        filterComponentVO.setShowType(filterComponent.getShowType());
        filterComponentVO.setPreFixedBtns(filterComponent.getPreFixedBtns());
        return filterComponentVO;
    }


    private static List<FilterButtonVO> buildFilterButtons(List<FilterButtonVO> filterBtns) {
        if (CollectionUtils.isEmpty(filterBtns)) {
            return null;
        }
        return filterBtns.stream()
                .map(filter -> buildFilterButtonVO(filter))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static FilterButtonVO buildFilterButtonVO(FilterButtonVO filterButtonVO) {
        if (filterButtonVO == null) {
            return null;
        }
        FilterButtonVO currentFilterButtonVO = new FilterButtonVO();
        currentFilterButtonVO.setTitle(filterButtonVO.getTitle());
        currentFilterButtonVO.setChildren(buildFilterButtons(filterButtonVO.getChildren()));
        currentFilterButtonVO.setSelected(filterButtonVO.isSelected());
        currentFilterButtonVO.setChildrenMinShowNum(filterButtonVO.getChildrenMinShowNum());
        currentFilterButtonVO.setSelectedTitle(filterButtonVO.getSelectedTitle());
        currentFilterButtonVO.setFilterBtnId(filterButtonVO.getFilterBtnId());
        return currentFilterButtonVO;
    }

    private static MainTitleComponentVO buildMainTitleComponentVO(MainTitleComponentVO mainTitle) {
        if (mainTitle == null) {
            return null;
        }
        MainTitleComponentVO mainTitleComponentVO = new MainTitleComponentVO();
        mainTitleComponentVO.setIcon(mainTitle.getIcon());
        mainTitleComponentVO.setJumpUrl(mainTitle.getJumpUrl());
        mainTitleComponentVO.setTitle(mainTitle.getTitle());
        mainTitleComponentVO.setTags(mainTitle.getTags());
        return mainTitleComponentVO;
    }

    private static PicAreaVO getPic(PicAreaVO pic) {
        if (pic == null) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(pic.getPic());
        return picAreaVO;
    }

    private static boolean invalidFilterBtnIdAndProAreasVO(FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO) {
        return filterBtnIdAndProAreasVO == null
                || CollectionUtils.isEmpty(filterBtnIdAndProAreasVO.getProductAreas())
                || filterBtnIdAndProAreasVO.getProductAreas().get(0) == null
                || filterBtnIdAndProAreasVO.getProductAreas().get(0).getItemArea() == null
                || CollectionUtils.isEmpty(filterBtnIdAndProAreasVO.getProductAreas().get(0).getItemArea().getProductItems());
    }
}
