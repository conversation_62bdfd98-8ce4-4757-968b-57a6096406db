package com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductCouponM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.AbstractDefaultProductVoBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ExtPointInstance(name = "婚纱礼服-货架落地页VO构造扩展点实现")
public class WeddingDressProductBuilderVoExt extends AbstractDefaultProductVoBuilderExt implements InitializingBean {

    private static final String ATTR_KEY_SALE_TYPE = "attr_weddingSaleType";

    private Map<String, FloorsBuilderExtAdapter> spuTypeAreaBuilderMap = null;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.wedding.dress.landing.product.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;

    @Resource
    private WeddingDressShelfItemAreaBuilderExt weddingDressShelfItemAreaBuilderExt;
    @Resource
    private WeddingTravelPhotoShelfItemAreaBuilderExt weddingTravelPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingIdPhotoShelfItemAreaBuilderExt weddingIdPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingShelfSimpleItemAreaBuilderExt weddingShelfSimpleItemAreaBuilderExt;
    @Resource
    private WeddingTrialShelfItemAreaBuilderExt weddingTrialShelfItemAreaBuilderExt;

    @Override
    public void afterPropertiesSet() {
        spuTypeAreaBuilderMap = new HashMap<String, FloorsBuilderExtAdapter>(4) {{
            // 婚纱礼服
            put("1342", weddingDressShelfItemAreaBuilderExt);
            // 旅拍婚纱照
            put("1345", weddingTravelPhotoShelfItemAreaBuilderExt);
            // 形象证件照
            put("1367", weddingIdPhotoShelfItemAreaBuilderExt);
            // 预约试纱
            put("2000117", weddingTrialShelfItemAreaBuilderExt);
        }};
    }

    @Override
    public List<String> productTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.productTags(ActivityContext,ProductM)");
        String spuType = productM.getAttr(QueryFetcher.Params.spuType);
        return Optional.ofNullable(spuTypeAreaBuilderMap.get(spuType)).orElse(weddingShelfSimpleItemAreaBuilderExt).itemComponentProductTags(activityContext, null, productM, 0);
    }

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.picUrl(ActivityContext,ProductM)");
        return PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), floorConstant.picWidth, floorConstant.picHeight, PictureURLBuilders.ScaleType.Cut);
    }

    @Override
    public List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.activityTags(ActivityContext,ProductM)");
        String activityIconUrl = productM.getAttr(GeneralProductAttrEnum.ATTR_SINGLE_ROW_SHELF_ICON.key);
        if (StringUtils.isEmpty(activityIconUrl)) {
            return null;
        }
        DzActivityTagVO dzActivityTagVO = new DzActivityTagVO();
        dzActivityTagVO.setImgUrl(activityIconUrl);
        dzActivityTagVO.setPosition(floorConstant.floatTagPosition);
        return Lists.newArrayList(dzActivityTagVO);
    }


    @Override
    public List<DzTagVO> aidDecisionTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.aidDecisionTags(ActivityContext,ProductM)");
        if (StringUtils.isEmpty(productM.getAttr(ATTR_KEY_SALE_TYPE))) {
            return null;
        }
        return Lists.newArrayList(buildDzTagVO(productM.getAttr(ATTR_KEY_SALE_TYPE)));
    }

    @Override
    public String coupons(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.coupons(ActivityContext,ProductM)");
        return Optional
                .ofNullable(productM)
                .map(ProductM::getCoupons)
                .filter(CollectionUtils::isNotEmpty)
                .map(coupons -> coupons.get(0))
                .map(ProductCouponM::getCouponTag)
                .orElse(null);
    }

    private DzTagVO buildDzTagVO (String tagName) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingDressProductBuilderVoExt.buildDzTagVO(java.lang.String)");
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setTagType(1);
        dzTagVO.setName(tagName);
        return dzTagVO;
    }

    @Data
    public static class FloorConstant {
        private int picWidth;
        private int picHeight;
        private int floatTagPosition;
    }
}
