package com.sankuai.dzviewscene.shelf.business.shelf.snapshot;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotDealMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.snapshot.builder.SnapshotDealOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.MultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformMultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeQueryOnlyProductsMultiABShelfFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.OnlyProductsShelfFlow;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 快照-POI页-团购货架二次请求
 * Created by liweilong06 on 2020/05/28
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "snapshot_poi_default_deal_shelf", name = "快照-POI页-团购货架二次请求")
public class SnapshotDealProductsTemplate implements IActivityTemplate {
    @Autowired
    private DouHuUtils douHuUtils;

    private static final String EXP_NAME = "floorsCntExp";

    //根据AB 获取 showType [20:对照组 42:实验组]
    private static final int OLD_SHOW_TYPE = 20;
    private static final int NEW_SHOW_TYPE = 42;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.snapshot.new.promo.switch", defaultValue = "false")
    private boolean promoSwitch;


    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.flow()");
        return MergeQueryOnlyProductsMultiABShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("团购"));

        // 2. 设置第一组商品参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put("团购", new HashMap<String, Object>() {{
                // 2.1 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, "10002175");
                put("promoTemplateId", 244);
                put("attributeKeys", buildAttrKeys());
                put("enablePreSalePromoTag", true);
                if (promoSwitch) {
                    put("directPromoScene", 400200);
                    put("priceDescType", 4);// 单列传4，双列传2
                    put("querySecKillSceneStrategy", 3);
                }
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());

            }});
        }});

        // 3. 设置商品平台组件和商品组映射关系
        exParams.put(QueryFetcher.Params.productComponent2Group, new HashMap<String, String>() {{
            put("团购"/*商品组件ID*/, "团购");
        }});
        // 4. 设置斗斛实验号
        List<String> expNames = Lists.newArrayList(EXP_NAME, SnapshotDealFilterProductsTemplate.HEAD_PIC_EXP_NAME);
        exParams.put(MultiDouHuFetcher.Params.DP_EXP_IDS, DouHuUtils.getDPDouHuExpIds("snapshot_poi_default_deal_shelf", expNames));
        exParams.put(MultiDouHuFetcher.Params.MT_EXP_IDS, DouHuUtils.getMTDouHuExpIds("snapshot_poi_default_deal_shelf", expNames));
        //根据结果 AB 获取 showType [20:对照组 42:实验组]
        int showType = queryPlanIdWithAB(exParams);
        //5. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, showType);
        //6. 设置商品数量
        exParams.put(ShelfActivityConstants.Params.pageSize, 100);
    }

    private List<String> buildAttrKeys() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.buildAttrKeys()");
        return Lists.newArrayList("service_type", "reservation_is_needed_or_not", "reservation_is_needed_or_not_2", "preSaleTag");
    }

    private int queryPlanIdWithAB(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.queryPlanIdWithAB(java.util.Map)");
        boolean isHit = douHuUtils.isHitExpSk(exParams, "snapshot_poi_default_deal_shelf", DouHuUtils.DouhuSkEnum.B_SK.getSk(), EXP_NAME);
        return isHit ? NEW_SHOW_TYPE : OLD_SHOW_TYPE;
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.extAbilities(java.util.Map)");
        /*斗斛AB侧实验能力*/
        abilities.put(MultiDouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformMultiDouHuFetcher.class);
        // 1. 查询使用默认能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, ShelfQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.SnapshotDealProductsTemplate.extPoints(java.util.Map)");
        // 1. 打点数据能力扩展点
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, SnapshotDealOceanBuilderExt.class);
        // 2. 商品楼层构造器扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, SnapshotDealFloorsBuilderExt.class);
        // 3. 标题扩展点
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, SnapshotDealMainTitleBuilderExt.class);
    }

}
