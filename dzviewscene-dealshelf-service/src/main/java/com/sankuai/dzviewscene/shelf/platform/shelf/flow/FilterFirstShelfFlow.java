package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 流程步骤:
 * 1. 斗斛数据
 * 2. 生成筛选
 * 3. 召回
 * 4. 填充
 *
 * 适应场景: 先生成筛选, 再根据筛选选中情况召回的场景
 *
 * Created by float on 2020/8/30.
 */
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = FilterFirstShelfFlow.ACTIVITY_FLOW_SHELF_WITH_FIRST_TAB_CODE, name = "先生成筛选再召回")
public class FilterFirstShelfFlow implements IActivityFlow<ShelfGroupM> {

    public static final String ACTIVITY_FLOW_SHELF_WITH_FIRST_TAB_CODE = "ShelfWithFirstTabFlow";

    @Override
    public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        // 1. 查询斗斛
        CompletableFuture<DouHuM> douHuMCompletableFuture = activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE).build(ctx);
        // 2. 生成筛选
        CompletableFuture<Map<String, FilterM>> filtersCompletableFuture = activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE).build(ctx);
        // 3. 召回: 商品组名->商品列表
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = filtersCompletableFuture.thenCompose(v -> {
            return activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx).thenCompose(productGroups -> {
                // 4. 暂存召回结果, 能力实现自行填充
                ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
                return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
            });

        });
        return CompletableFuture.allOf(douHuMCompletableFuture, filtersCompletableFuture, productGroupsCompletableFuture).thenApply(v -> {
            ShelfGroupM shelfGroupM = new ShelfGroupM();
            shelfGroupM.setDouHu(douHuMCompletableFuture.join());
            shelfGroupM.setFilterMs(filtersCompletableFuture.join());
            shelfGroupM.setProductGroupMs(productGroupsCompletableFuture.join());
            return shelfGroupM;
        });
    }
}
