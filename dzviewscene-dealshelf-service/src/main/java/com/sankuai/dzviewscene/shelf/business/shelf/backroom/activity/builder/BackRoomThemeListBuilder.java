package com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder;

import com.dianping.cat.Cat;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.ShelfShopRecProduct;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.data.Converters;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.enums.CardTypeEnum;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.enums.CardHoldStatusEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfCardBarVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.context.BackroomRecommendContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.CardHoldStatusContext;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.JoyCardShelfContext;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.MultiDouHuExtContext;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.business.utils.UrlUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankerEnum;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingRequest;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingRequestParamEnum;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.DefaultBatchRankingService;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Created by float.lu on 2020/9/18.
 */
@AbilityInstance(name = "密室预订商品列表VO构造能力实例")
public class BackRoomThemeListBuilder extends ThemeListBuilder {

    @Resource
    private DefaultBatchRankingService rankingService;

    /**
     * 默认展示样式
     */
    private static final int SHOW_TYPE_DEFAULT = 1;

    /**
     * AB后的新版展示样式
     */
    private static final int SHOW_TYPE_DOU_HU = 2;

    /**
     * 性能优化文案的后缀
     */
    private static final String OPTIMIZED_SUFFIX = "_optimized";

    /**
     * 批量排序的默认key
     */
    private static final String MULTI_RANK_MAP_KEY = "defaultKey";

    /**
     * 标题前的订icon
     */
    private static final String DP_BOOKING_TITLE_ICON = "https://p0.meituan.net/scarlett/f1ffa45a72da7a167b5a0ef9d14483ca879.png";

    private static final String MT_BOOKING_TITLE_ICON = "https://p0.meituan.net/scarlett/45cea411e4ee5c4779e8a5313b0094d4883.png";

    /**
     * 搜索可解释性优化斗斛
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.backroom.search.douhu.codes", defaultValue = "")
    private List<String> douHuHitCodesForSearch;

    /**
     * 新老样式斗斛开关
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.backroom.showinfo.douhu.codes", defaultValue = "")
    private List<String> douHuHitCodesForShow;

    /**
     * 忽略斗斛设置，直接使用重构后的样式开关
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.backroom.showinfo.newStyle.switch", defaultValue = "false")
    private boolean directUseNewStyle;

    @Override
    public CompletableFuture<BackroomThemeListVO> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<Map<String, ProductGroupM>> productGroupMCompletableFuture = ctx.getMainData();
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        ShopM shopM = ctx.getParam(ShelfActivityConstants.Ctx.ctxShop);
        CompletableFuture<Map<String, String>> cardBarFuture = ctx.getExtContext(JoyCardShelfContext.CONTEXT_KEY);
        CompletableFuture<CardHoldStatusDTO> cardHoldStatusDTOFuture = ctx.getExtContext(CardHoldStatusContext.CONTEXT_KEY);
        CompletableFuture<Response<List<ShelfShopRecProduct>>> shopRecommendProductsFuture = ctx.getExtContext(BackroomRecommendContextExt.CONTEXT_KEY);
        return CompletableFuture.allOf(productGroupMCompletableFuture, cardBarFuture, cardHoldStatusDTOFuture, shopRecommendProductsFuture).thenApply(avoid ->
                buildBackroomThemeListVO(productGroupMCompletableFuture, cardBarFuture, cardHoldStatusDTOFuture, shopRecommendProductsFuture, platform, shopM, ctx));
    }

    private BackroomThemeListVO buildBackroomThemeListVO(CompletableFuture<Map<String, ProductGroupM>> productMFuture, CompletableFuture<Map<String, String>> cardBarDTOFuture, CompletableFuture<CardHoldStatusDTO> cardHoldStatusDTOFuture,
                                                         CompletableFuture<Response<List<ShelfShopRecProduct>>> shopRecommendProductsFuture, int platform, ShopM shopM, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "c.s.d.s.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBackroomThemeListVO(CompletableFuture,CompletableFuture,CompletableFuture,CompletableFuture,int,ShopM,ActivityContext)");
        Map<String, ProductGroupM> productMap = productMFuture.join();
        Map<String, String> cardBarDTO = cardBarDTOFuture.join();
        CardHoldStatusDTO cardHoldStatusDTO = cardHoldStatusDTOFuture.join();
        Response<List<ShelfShopRecProduct>> shopRecommendProducts = shopRecommendProductsFuture.join();
        if (MapUtils.isEmpty(productMap) || CollectionUtils.isEmpty(productMap.values())) {
            return null;
        }
        ProductGroupM productGroupM = Lists.newArrayList(productMap.values()).get(0);
        BackroomThemeListVO result = new BackroomThemeListVO();
        result.setTitle("主题预订");
        result.setIcon(PlatformUtil.isMT(platform) ? MT_BOOKING_TITLE_ICON : DP_BOOKING_TITLE_ICON);
        result.setCardBar(buildCardBar(cardBarDTO));
        result.setThemeList(buildThemeList(productGroupM, shopRecommendProducts, ctx));
        result.setShowType(getShowType(ctx));
        result.setAbTest(getABTestInfo(ctx));
        fillOcean(cardHoldStatusDTO, result, shopM);
        return result;
    }

    private String getABTestInfo(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getABTestInfo(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<DouHuM> multiDouHu = getDouHuResults(activityContext);
        if (CollectionUtils.isEmpty(multiDouHu)) {
            return null;
        }
        List<String> abTestList = Converters.newPropertyExtractorConverter("abtest").convert(multiDouHu);
        return JsonCodec.encode(abTestList);
    }

    private int getShowType(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getShowType(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (isDouHuHit(ctx, douHuHitCodesForShow)) {
            return SHOW_TYPE_DOU_HU;
        }
        return SHOW_TYPE_DEFAULT;
    }

    private void fillOcean(CardHoldStatusDTO cardHoldStatusDTO, BackroomThemeListVO result, ShopM shopM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.fillOcean(CardHoldStatusDTO,BackroomThemeListVO,ShopM)");
        boolean shopHasJoyCard = false;
        boolean userHasJoyCard = false;
        if (cardHoldStatusDTO != null) {
            List<Integer> shopCardHold = cardHoldStatusDTO.getShopHasCardTypeList();
            List<Integer> userCardHold = cardHoldStatusDTO.getUserHoldCardTypeList();
            shopHasJoyCard = shopCardHold.stream().anyMatch(cardType -> cardType == CardTypeEnum.JOY_CARD.getCode());
            userHasJoyCard = userCardHold.stream().anyMatch(cardType -> cardType == CardTypeEnum.JOY_CARD.getCode());
        }
        //密室目前仅有玩乐卡
        result.setCardTypeOcean(shopHasJoyCard ? CardHoldStatusEnums.JOY_CARD.getStatus() : CardHoldStatusEnums.OFFLINE.getStatus());
        result.setMemberProfileOcean(userHasJoyCard ? CardHoldStatusEnums.JOY_CARD.getStatus() : CardHoldStatusEnums.OFFLINE.getStatus());
        result.setCategoryId(shopM == null ? 0 : shopM.getCategory());
    }

    private List<BackroomThemeVO> buildThemeList(ProductGroupM productGroupM, Response<List<ShelfShopRecProduct>> shopRecommendProducts, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildThemeList(ProductGroupM,Response,ActivityContext)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return null;
        }
        List<BackroomThemeVO> result = productGroupM.getProducts().stream()
                .sorted(recommendComparator(shopRecommendProducts))
                .map(productM -> buildThemeVO(productM, ctx))
                .collect(Collectors.toList());
        // 因为搜素可解释性优化是在其他置顶规则基础上执行的，所以一定要等前面排好了才能调用
        result = sortForSearchTop(ctx, result);
        //密室锚点逻辑，指向的商品为商家推荐商品中的置顶位置
        if (result.size() >= 3) {
            Collections.swap(result, 0, 1);
        }
        return result;
    }

    private List<BackroomThemeVO> sortForSearchTop(ActivityContext ctx, List<BackroomThemeVO> result) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.sortForSearchTop(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        if (!isDouHuHit(ctx, douHuHitCodesForSearch)) {
            return result;
        }
        BatchRankingRequest batchRankingRequest = buildBatchRankingRequest(ctx, result);
        Map<String, List<RankingItem>> rankResult = rankingService.batchRanking(batchRankingRequest);
        if (MapUtils.isEmpty(rankResult) || CollectionUtils.isEmpty(rankResult.get(MULTI_RANK_MAP_KEY))) {
            return result;
        }
        return result.stream().sorted(searchTopComparator(rankResult.get(MULTI_RANK_MAP_KEY))).collect(Collectors.toList());
    }

    private Comparator<BackroomThemeVO> searchTopComparator(List<RankingItem> rankingItems) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.searchTopComparator(java.util.List)");
        Map<Integer, Integer> productId2IndexMap = Maps.newHashMap();
        for (int i = 0; i < rankingItems.size(); i++) {
            ProductM productM = (ProductM) rankingItems.get(i);
            productId2IndexMap.put(productM.getProductId(), i);
        }
        return (o1, o2) -> {
            // 根据shelfShopRecProductList里面的顺序来排序
            int index1 = Optional.ofNullable(productId2IndexMap.get(o1.getProductId())).orElse(Integer.MAX_VALUE);
            int index2 = Optional.ofNullable(productId2IndexMap.get(o2.getProductId())).orElse(Integer.MAX_VALUE);
            return index1 - index2;
        };
    }

    private BatchRankingRequest buildBatchRankingRequest(ActivityContext ctx, List<BackroomThemeVO> result) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBatchRankingRequest(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        BatchRankingRequest rankingRequest = new BatchRankingRequest();
        rankingRequest.setSource(buildBatchRankingItem(result));
        rankingRequest.setBatchRankers(Lists.newArrayList(BatchRankerEnum.Recommend.name()));
        rankingRequest.getBatchRankParams().putAll(buildBatchRankingRequestExtParams(ctx));
        return rankingRequest;
    }

    private Map<String, Object> buildBatchRankingRequestExtParams(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBatchRankingRequestExtParams(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Map<String, Object> extParams = new HashMap<>();
        putIfNotNull(extParams, BatchRankingRequestParamEnum.Platform.getKey(), ctx.getParam(ShelfActivityConstants.Params.platform));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.UnionId.getKey(), ctx.getParam(ShelfActivityConstants.Params.unionId));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.DpCityId.getKey(), ctx.getParam(ShelfActivityConstants.Params.dpCityId));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.MtCityId.getKey(), ctx.getParam(ShelfActivityConstants.Params.mtCityId));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.DpUserId.getKey(), ctx.getParam(ShelfActivityConstants.Params.dpUserId));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.MtUserId.getKey(), ctx.getParam(ShelfActivityConstants.Params.mtUserId));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.ShopRecommendProductIds.getKey(), getTopProductIds(ctx));
        putIfNotNull(extParams, BatchRankingRequestParamEnum.ShelfType.getKey(), ShelfTypeEnums.BOOK.getType());
        return extParams;
    }

    private List<Integer> getTopProductIds(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getTopProductIds(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        String summaryProductIds = ctx.getParam(ShelfActivityConstants.Params.summaryProductIds);
        String productIds = ctx.getParam(ShelfActivityConstants.Params.topProductIds);
        return ParamUtil.getSummarySpuIds(summaryProductIds, productIds);
    }

    private void putIfNotNull(Map<String, Object> extParams, String key, Object value) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.putIfNotNull(java.util.Map,java.lang.String,java.lang.Object)");
        if (StringUtils.isEmpty(key) || value == null) {
            return;
        }
        extParams.put(key, value);
        return;
    }

    private Map<String, List<RankingItem>> buildBatchRankingItem(List<BackroomThemeVO> result) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBatchRankingItem(java.util.List)");
        Map<String, List<RankingItem>> rankingItemMap = new HashMap<>();
        List<RankingItem> rankingItems = result.stream()
                .map(backroomThemeVO -> {
                    ProductM productM = new ProductM();
                    productM.setProductId(backroomThemeVO.getProductId());
                    return productM;
                })
                .collect(Collectors.toList());
        rankingItemMap.put(MULTI_RANK_MAP_KEY, rankingItems);
        return rankingItemMap;
    }

    private Comparator<ProductM> recommendComparator(Response<List<ShelfShopRecProduct>> shopRecommendProducts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.recommendComparator(com.dianping.product.shelf.common.dto.Response)");
        if (Objects.isNull(shopRecommendProducts) || CollectionUtils.isEmpty(shopRecommendProducts.getContent())) {
            return Comparator.comparing(ProductM::getTitle);
        }
        List<ShelfShopRecProduct> shelfShopRecProductList = shopRecommendProducts.getContent();
        //构建商家推荐商品ID到位次映射的map的Map
        Map<Integer, Integer> productId2IndexMap = Maps.newHashMap();
        for (int i = 0; i < shelfShopRecProductList.size(); i++) {
            productId2IndexMap.put(shelfShopRecProductList.get(i).getProductID().intValue(), i);
        }
        return (o1, o2) -> {
            // 根据shelfShopRecProductList里面的顺序来排序
            int index1 = Optional.ofNullable(productId2IndexMap.get(o1.getProductId())).orElse(Integer.MAX_VALUE);
            int index2 = Optional.ofNullable(productId2IndexMap.get(o2.getProductId())).orElse(Integer.MAX_VALUE);
            return index1 - index2;
        };
    }

    private BackroomThemeVO buildThemeVO(ProductM productM, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildThemeVO(ProductM,ActivityContext)");
        if (productM == null) {
            return null;
        }
        BackroomThemeVO backroomThemeVO = new BackroomThemeVO();
        backroomThemeVO.setProductId(productM.getProductId());
        backroomThemeVO.setProductName(productM.getTitle());
        backroomThemeVO.setRichLabelsProductName(buildRichLabelProductName(ctx, productM.getTitle()));
        backroomThemeVO.setBookingNum(productM.getSaleTag());
        backroomThemeVO.setDetailUrl(getDetailUrl(ctx, productM));
        backroomThemeVO.setHeadPic(productM.getPicUrl());
        backroomThemeVO.setDifficultLevel(NumberUtils.toInt(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_DIFFICULTLEVEL.getKey())));
        backroomThemeVO.setHuiIcon(findAttrValueFromProductM(productM, String.format("%s%s", GeneralProductAttrEnum.ATTR_BACKROOM_HUIICON.getKey(), OPTIMIZED_SUFFIX)));
        backroomThemeVO.setPinIcon(findAttrValueFromProductM(productM, String.format("%s%s", GeneralProductAttrEnum.ATTR_BACKROOM_PINICONURL.getKey(), OPTIMIZED_SUFFIX)));
        backroomThemeVO.setVideoUrl(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_VIDEO_URL.getKey()));
        backroomThemeVO.setRankInfo(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_RANK.getKey()));
        backroomThemeVO.setStyle(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_STYLE.getKey()));
        backroomThemeVO.setPoolRuleDesc(buildPoolRuleDescVO(productM));
        backroomThemeVO.setTags(buildBackroomTags(productM, ctx));
        backroomThemeVO.setTips(buildBackroomTips(productM, ctx));
        backroomThemeVO.setScore(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_PRODUCT_REVIEW_SCORE.getKey()));
        return backroomThemeVO;
    }

    private String buildRichLabelProductName(ActivityContext ctx, String title) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildRichLabelProductName(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(ctx, title, ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD);
    }

    private String getDetailUrl(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getDetailUrl(ActivityContext,ProductM)");
        if (isDouHuHit(ctx, douHuHitCodesForShow)) {
            return buildProductDetailUrl(ctx, productM);
        }
        return productM.getJumpUrl();
    }

    private String buildProductDetailUrl(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildProductDetailUrl(ActivityContext,ProductM)");
        int uaCode = getUaCode(ctx);
        long shopId = getShopId(ctx);
        return UrlUtils.getAPPUrl(uaCode, "/ssr/biz-product-detail/play-theme-detail.html?source=1&shopid=" + shopId + "&productid=" + productM.getProductId());

    }

    private int getUaCode(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getUaCode(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int uaCode = ParamsUtil.getIntSafely(ctx, ProductDetailActivityConstants.Params.userAgent);
        if (uaCode > 0) {
            return uaCode;
        }
        int platform = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return 200;
        }
        return 100;
    }

    private Long getShopId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = activityContext.getParam(FilterListActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, FilterListActivityConstants.Params.mtPoiIdL, FilterListActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, FilterListActivityConstants.Params.dpPoiIdL, FilterListActivityConstants.Params.dpPoiId);
    }

    private PoolRuleDescVO buildPoolRuleDescVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildPoolRuleDescVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        PoolRuleInfoData poolRuleInfo = JsonCodec.decode(findAttrValueFromProductM(productM, String.format("%s%s", GeneralProductAttrEnum.ATTR_BACKROOM_POOLRULEINFO.getKey(), OPTIMIZED_SUFFIX)), PoolRuleInfoData.class);
        if (Objects.isNull(poolRuleInfo)) {
            return null;
        }
        PoolRuleDescVO poolRuleDescVO = new PoolRuleDescVO();
        poolRuleDescVO.setTitle(poolRuleInfo.title);
        poolRuleDescVO.setItemList(buildPoolRuleItemDescVOList(poolRuleInfo.poolRuleItems));
        return poolRuleDescVO;
    }

    private List<PoolRuleItemDescVO> buildPoolRuleItemDescVOList(List<PoolRuleItemInfoData> poolRuleItemInfoDataList) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildPoolRuleItemDescVOList(java.util.List)");
        if (CollectionUtils.isEmpty(poolRuleItemInfoDataList)) {
            return null;
        }
        return poolRuleItemInfoDataList.stream().map(this::buildPoolRuleItemVO).collect(Collectors.toList());
    }

    private PoolRuleItemDescVO buildPoolRuleItemVO(PoolRuleItemInfoData poolRuleItemInfoData) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildPoolRuleItemVO(BackRoomThemeListBuilder$PoolRuleItemInfoData)");
        if (Objects.isNull(poolRuleItemInfoData)) {
            return null;
        }
        PoolRuleItemDescVO poolRuleItemDescVO = new PoolRuleItemDescVO();
        poolRuleItemDescVO.setTitle(poolRuleItemInfoData.title);
        poolRuleItemDescVO.setContent(buildContentLineVOList(poolRuleItemInfoData.contents));
        return poolRuleItemDescVO;
    }

    private List<PoolRuleContentLineVO> buildContentLineVOList(List<List<TextItemData>> contents) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildContentLineVOList(java.util.List)");
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        return contents.stream().map(this::buildContentLineVO).collect(Collectors.toList());
    }

    private PoolRuleContentLineVO buildContentLineVO(List<TextItemData> contentLine) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildContentLineVO(java.util.List)");
        if (CollectionUtils.isEmpty(contentLine)) {
            return null;
        }
        PoolRuleContentLineVO poolRuleContentLineVO = new PoolRuleContentLineVO();
        poolRuleContentLineVO.setContentLine(contentLine.stream().map(this::buildRichLabel).collect(Collectors.toList()));
        return poolRuleContentLineVO;
    }

    private RichLabelVO buildRichLabel(TextItemData textItemData) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildRichLabel(BackRoomThemeListBuilder$TextItemData)");
        if (Objects.isNull(textItemData)) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(textItemData.text);
        richLabelVO.setTextColor(textItemData.textcolor);
        richLabelVO.setTextSize(textItemData.textsize);
        return richLabelVO;
    }


    /**
     * 密室提示包括密室起订数，建议人数，时长
     *
     * @return
     */
    private List<String> buildBackroomTips(ProductM productM, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBackroomTips(ProductM,ActivityContext)");
        if (isDouHuHit(ctx, douHuHitCodesForShow)) {
            String startNum = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_STARTNUM.getKey());
            String adviceNum = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_ROLEPLAY_PLAYERS_ADVICENUM.getKey());
            String duration = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_PURE_DURATION.getKey());
            return Lists.newArrayList(startNum, adviceNum, duration).stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        String startNum = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_STARTNUM.getKey());
        String adviceNum = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_ADVICENUM.getKey());
        String duration = findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_DURATION.getKey());
        return Lists.newArrayList(startNum, adviceNum, duration).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private boolean isDouHuHit(ActivityContext activityContext, List<String> expSks) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.isDouHuHit(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        if (directUseNewStyle || CollectionUtils.isEmpty(expSks)) {
            return true;
        }
        List<DouHuM> douHus = getDouHuResults(activityContext);
        if (CollectionUtils.isEmpty(douHus)) {
            return false;
        }
        return douHus.stream()
                .filter(douHuM -> expSks.contains(douHuM.getSk()))
                .findAny().orElse(null) != null;
    }

    private List<DouHuM> getDouHuResults(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.getDouHuResults(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<List<DouHuM>> extContextFuture = activityContext.getExtContext(MultiDouHuExtContext.CONTEXT_KEY);
        if (extContextFuture == null) {
            return null;
        }
        return extContextFuture.join();
    }

    private List<String> buildBackroomTags(ProductM productM, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildBackroomTags(ProductM,ActivityContext)");
        if (isDouHuHit(ctx, douHuHitCodesForShow)) {
            return productM.getProductTags();
        }
        return JsonCodec.decode(findAttrValueFromProductM(productM, GeneralProductAttrEnum.ATTR_BACKROOM_TAGS.getKey()), new TypeReference<List<String>>() {
        });
    }

    private DzShelfCardBarVO buildCardBar(Map<String, String> productShelfJoyCardMap) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.buildCardBar(java.util.Map)");
        if (productShelfJoyCardMap == null) {
            return null;
        }
        DzShelfCardBarVO cardBar = new DzShelfCardBarVO();
        cardBar.setIconUrl(productShelfJoyCardMap.get("iconUrl"));
        cardBar.setJumpUrl(productShelfJoyCardMap.get("jumpUrl"));
        cardBar.setTitle(productShelfJoyCardMap.get("title"));
        return cardBar;
    }

    private String findAttrValueFromProductM(ProductM productM, String key) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.BackRoomThemeListBuilder.findAttrValueFromProductM(ProductM,String)");
        if (productM == null || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        return productM.getExtAttrs().stream().filter(attr -> attr.getName().equals(key)).findFirst().map(AttrM::getValue).orElse(null);
    }

    //富文本内部类，文案层对象，方便反序列化
    @Data
    private static class TextItemData {

        public TextItemData() {
        }

        /**
         * 内容
         */
        private String text;

        /**
         * 颜色
         */
        private String textcolor;

        /**
         * 文字大小
         */
        private int textsize;

    }

    //拼场规则内部类，文案层对象，方便反序列化
    @Data
    private static class PoolRuleInfoData {

        public PoolRuleInfoData() {
        }

        /**
         * 拼场弹窗总标题
         */
        private String title;

        /**
         * 拼场弹窗子模块集合
         */
        private List<PoolRuleItemInfoData> poolRuleItems;

    }

    //拼场规则子模块内部类，文案层对象，方便反序列化
    @Data
    private static class PoolRuleItemInfoData {

        public PoolRuleItemInfoData() {
        }

        /**
         * 拼场弹窗子模块标题
         */
        private String title;

        /**
         * 拼场弹窗子模块内容
         */
        private List<List<TextItemData>> contents;

    }

}


