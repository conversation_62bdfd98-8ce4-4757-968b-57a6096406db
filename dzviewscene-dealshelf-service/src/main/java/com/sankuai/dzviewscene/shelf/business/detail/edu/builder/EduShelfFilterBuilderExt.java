package com.sankuai.dzviewscene.shelf.business.detail.edu.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ClientTypeUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.RichLabelFrontWeightEnums;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.edu.config.EduShelfLionConfig;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzviewscene.shelf.business.shelf.edu.config.EduShelfLionConfig.EDU_SHELF_LION_CONFIG_KEY;


/**
 * 教育货架过滤组件扩展点实现
 * <AUTHOR>
 */
@ExtPointInstance(name = "教育货架过滤组件扩展点实现")
public class EduShelfFilterBuilderExt extends FilterBuilderExtAdapter {

    @ConfigValue(key = EDU_SHELF_LION_CONFIG_KEY)
    private EduShelfLionConfig eduShelfLionConfig;

    @Override
    public int showType(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        CompletableFuture<ShelfGroupM> shelfMCompletableFuture = activityContext.getMainData();
        if (shelfMCompletableFuture == null) {
            return 1;
        }
        ShelfGroupM shelfGroupM = shelfMCompletableFuture.join();
        if (notFilter(shelfGroupM.getFilterMs(), productType)) {
            return 1;
        }
        if (noChildren(shelfGroupM.getFilterMs(), productType)) {
            // 单层筛选栏返回2
            return 2;
        }
        return 1;
    }

    private boolean noChildren(Map<String, FilterM> filterMs, String productType) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.noChildren(java.util.Map,java.lang.String)");
        Set<Boolean> hasChildrenSet = new HashSet<>();
        if (filterMs.get(productType) == null || CollectionUtils.isEmpty(filterMs.get(productType).getFilters())) {
            return true;
        }
        for (FilterBtnM filter : filterMs.get(productType).getFilters()) {
            hasChildrenSet.add(CollectionUtils.size(filter.getChildren()) >= getChildrenMinShowNum());
        }
        return !hasChildrenSet.contains(Boolean.TRUE);
    }

    private boolean notFilter(Map<String, FilterM> filterMs, String productType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.notFilter(java.util.Map,java.lang.String)");
        return MapUtils.isEmpty(filterMs) || filterMs.get(productType) == null || CollectionUtils.isEmpty(filterMs.get(productType).getFilters());
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String groupName) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return eduShelfLionConfig.getFilterMinShowNum() == 0 ? 1 : eduShelfLionConfig.getFilterMinShowNum();
    }

    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String groupName) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.childrenMinShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return getChildrenMinShowNum();
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.labs(ActivityContext,FilterBtnM)");
        return null;
    }

    private int getChildrenMinShowNum() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.getChildrenMinShowNum()");
        return eduShelfLionConfig.getChildrenMinShowNum() == 0 ? 2 : eduShelfLionConfig.getChildrenMinShowNum();
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.titleButton(ActivityContext,String,FilterBtnM)");
        if (filterBtnM == null) {
            return null;
        }
        if(StringUtils.isNotEmpty(PerfectActivityBuildUtils.getActivityName()) && PerfectActivityBuildUtils.getActivityName().equals(filterBtnM.getTitle())) {
            RichLabelVO richLabelVO = new RichLabelVO();
            richLabelVO.setText(filterBtnM.getTitle());
            richLabelVO.setTextColor(PerfectActivityBuildUtils.getSelectedFilterColor());
            if(PerfectActivityBuildUtils.getSelectedFilterSize() > 0) {
                richLabelVO.setTextSize(PerfectActivityBuildUtils.getSelectedFilterSize());
            }
            return richLabelVO;
        }
        return buildFilterTitle(filterBtnM.getTitle(), filterBtnM.isSelected(), CollectionUtils.isEmpty(filterBtnM.getChildren()) ? 2 : 1, activityContext.getParameters());
    }


    @Override
    public RichLabelVO selectedTitleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.selectedTitleButton(ActivityContext,String,FilterBtnM)");
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(filterBtnM.getTitle());
        if(StringUtils.isNotEmpty(PerfectActivityBuildUtils.getActivityName()) && PerfectActivityBuildUtils.getActivityName().equals(filterBtnM.getTitle())) {
            richLabelVO.setTextColor(PerfectActivityBuildUtils.getSelectedFilterColor());
            if(PerfectActivityBuildUtils.getSelectedFilterSize() > 0) {
                richLabelVO.setTextSize(PerfectActivityBuildUtils.getSelectedFilterSize());
            }
            return richLabelVO;
        }
        return richLabelVO;
    }


    /**
     * 构造筛选组件：title
     */
    private RichLabelVO buildFilterTitle(String name, boolean selected, int level, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.buildFilterTitle(java.lang.String,boolean,int,java.util.Map)");
        if (ClientTypeUtils.getPlatformFromClientType(Integer.parseInt(params.getOrDefault(ShelfActivityConstants.Params.platform, "1").toString())) == VCPlatformEnum.DP.getType()) {
            return buildDpFilterTitle(name, selected, level);
        }
        return buildMtFilterTitle(name, selected, level);
    }

    private RichLabelVO buildMtFilterTitle(String name, boolean selected, int level) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.buildMtFilterTitle(java.lang.String,boolean,int)");
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(name);
        richLabelVO.setTextColor(level == 1 ? "#2F2F2F" : "#666666");
        richLabelVO.setTextSize(level == 1 ? 28 : 26);
        richLabelVO.setFontWeight(RichLabelFrontWeightEnums.NORMAL.getType());
        if (level != 1 && selected) {
            richLabelVO.setFontWeight(RichLabelFrontWeightEnums.BOLD.getType());
        }
        return richLabelVO;
    }

    private RichLabelVO buildDpFilterTitle(String name, boolean selected, int level) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.buildDpFilterTitle(java.lang.String,boolean,int)");
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(name);
        richLabelVO.setTextColor("#111111");
        richLabelVO.setTextSize(level == 1 ? 28 : 26);
        richLabelVO.setFontWeight(RichLabelFrontWeightEnums.NORMAL.getType());
        if (level != 1 && selected) {
            richLabelVO.setTextColor(ColorUtils.colorFF6633);
            richLabelVO.setFontWeight(RichLabelFrontWeightEnums.BOLD.getType());
        }
        return richLabelVO;
    }

    @Override
    public RichLabelVO subTitle(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.subTitle(ActivityContext,String,FilterBtnM)");
        return null;
    }

    @Override
    public String extra(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.extra(ActivityContext,String,FilterBtnM)");
        return null;
    }

    @Override
    public List<IconRichLabelVO> preFixedBtns(ActivityContext activityContext, String groupName) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.preFixedBtns(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return null;
    }

    @Override
    public boolean showWithNoProducts(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduShelfFilterBuilderExt.showWithNoProducts(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return false;
    }

}
