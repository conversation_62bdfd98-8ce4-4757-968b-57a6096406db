package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability;

import com.dianping.cat.Cat;
import com.dianping.joy.booking.api.generalpool.enums.PoolStatusEnum;
import com.dianping.tpfun.product.api.sku.common.enums.SPUTypeEnum;
import com.dianping.userremote.base.enums.SexEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.productdetail.util.UrlUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.filterlist.utils.PinPoolInfoPaddingUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.WeekEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductVoBuilder;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.SCPResourceAttrEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5.12.21 3:08 下午
 **/
@AbilityInstance(name = "开店宝拼车场次构造")
public class MerchantPinListProductVoBuilder extends ProductVoBuilder {

    private static final String EMPTY = "";

    private static final String PURCHASE = "%S %S %S";
    private static final int CAN_SHARE = 1;
    private static final int UN_CAN_SHARE = 0;

    private static final int ON_LINE = 0;
    private static final int OFF_LINE = 1;

    private static final List<Integer> POOL_FAILED = Lists.newArrayList(PoolStatusEnum.NOT_ENOUGH.getCode(),
                                                                        PoolStatusEnum.OTHER_PERSON_BAO_CHANG.getCode(),
                                                                        PoolStatusEnum.PRODUCT_CHANGE.getCode(),
                                                                        PoolStatusEnum.WHOLE_REFUND.getCode(),
                                                                        PoolStatusEnum.INIT.getCode());

    private static final List<Integer> POOL_SUCCESS = Lists.newArrayList(PoolStatusEnum.RE_POOLING.getCode(),
                                                                         PoolStatusEnum.POOL_SUCCESS.getCode());

    private static final List<Integer> POOLING_AND_SUCCESS = Lists.newArrayList(PoolStatusEnum.POOLING.getCode(),
                                                                            PoolStatusEnum.RE_POOLING.getCode(),
                                                                            PoolStatusEnum.POOL_SUCCESS.getCode());

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.pin.pool.dpCityId", defaultValue = "[]")
    private List<Integer> dpCityIdList;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.pin.pool.merchant", defaultValue = "{}")
    private Map<String, String> merchantMap;

    @Override
    public CompletableFuture<List<DzProductVO>> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<FilterProductsM> filterProductsMCompletableFuture = ctx.getMainData();
        if (filterProductsMCompletableFuture == null) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        return filterProductsMCompletableFuture.thenApply(filterProductsM -> buildProductFromPoolInfos(ctx, filterProductsM.getProductGroup()));
    }

    private List<DzProductVO> buildProductFromPoolInfos(ActivityContext ctx, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildProductFromPoolInfos(ActivityContext,ProductGroupM)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return Lists.newArrayList();
        }
        List<DzProductVO> productVOS = new ArrayList<>();
        productGroupM.getProducts().stream()
                .filter(productM -> CollectionUtils.isNotEmpty(productM.getPinPools()))
                .map(productM -> {
                    List<ProductPinPoolM> pinPools = productM.getPinPools();
                    productVOS.addAll(pinPools.stream().filter(pinPool -> filterPoolInfo(ctx, pinPool))
                                              .map(productPinPoolM -> buildDzProductVO(ctx, productM, productPinPoolM))
                                              .collect(Collectors.toList()));
                    return productVOS;
                }).collect(Collectors.toList());
        return productVOS.stream().sorted(Comparator.comparing(DzProductVO::getActivityRemainSeconds)).collect(Collectors.toList());
    }

    private boolean filterPoolInfo(ActivityContext ctx, ProductPinPoolM pinPool) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.filterPoolInfo(ActivityContext,ProductPinPoolM)");
        List<Integer> poolStatusList = ParamsUtil.getValue(ctx, QueryFetcher.Params.poolStatus, POOLING_AND_SUCCESS);
        if (poolStatusList.contains(PoolStatusEnum.POOLING.getCode())) {
            if (pinPool.getPoolStatus() == PoolStatusEnum.POOL_SUCCESS.getCode()) {
                return pinPool.isCanJoin();
            }
            return true;
        }
        if (poolStatusList.size() == 1 && poolStatusList.contains(PoolStatusEnum.POOL_SUCCESS.getCode())) {
            if (pinPool.getPoolStatus() == PoolStatusEnum.POOL_SUCCESS.getCode()) {
                return !pinPool.isCanJoin();
            }
            return false;
        }
        return true;
    }

    private DzProductVO buildDzProductVO(ActivityContext ctx, ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildDzProductVO(ActivityContext,ProductM,ProductPinPoolM)");
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setProductId((int)productPinPoolM.getPoolId());
        dzProductVO.setProductIdL(productPinPoolM.getPoolId());
        dzProductVO.setTitle(productM.getTitle());
        dzProductVO.setPic(productM.getPicUrl());
        dzProductVO.setActivityRemainSeconds(productPinPoolM.getBookStartTime());
        dzProductVO.setPurchase(buildPurchase(productPinPoolM.getBookStartTime()));
        dzProductVO.setButtonStatus(buildButtonStatus(productPinPoolM));
        dzProductVO.setProductTags(buildProductTags(productM, ctx));
        dzProductVO.setProductDesc(buildProductDesc(productM, productPinPoolM));
        dzProductVO.setAidDecisionTags(buildAidDecisionTags(productPinPoolM));
        dzProductVO.setPoolInfoList(buildPoolInfoList(ctx, productPinPoolM));
        dzProductVO.setExtAttrsStr(buildExtAttrsStr(ctx, productM, productPinPoolM));
        return dzProductVO;
    }

    private String buildProductDesc(ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildProductDesc(ProductM,ProductPinPoolM)");
        return buildDzTagName(buildPurchase(productPinPoolM.getBookStartTime()), productM);
    };


    private List<String> buildProductTags(ProductM productM,ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildProductTags(ProductM,ActivityContext)");
        int spuType = ParamsUtil.getIntSafely(ctx, QueryFetcher.Params.spuType);
        String totalNum = productM.getAttr(SCPResourceAttrEnum.ATTR_ROLEPLAY_PLAYERS_NUMBER_STR.getKey());
        String peopleForGender = productM.getAttr(SCPResourceAttrEnum.ATTR_ROLEPLAY_PLAYERS_MAN_AND_WOMAN.getKey());
        String duration = productM.getAttr(getDuration(spuType));
        return Lists.newArrayList(totalNum, peopleForGender, duration).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String getDuration(int spuType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.getDuration(int)");
        return spuType == SPUTypeEnum.BackRoom.getCode() ? SCPResourceAttrEnum.ATTR_ACTIVE_ROLEPLAY_DURATION.getKey()
                : SCPResourceAttrEnum.ATTR_ROLEPLAY_DURATION.getKey();
    }

    private String buildExtAttrsStr(ActivityContext ctx, ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildExtAttrsStr(ActivityContext,ProductM,ProductPinPoolM)");
        Map<String, Object> extAttr = Maps.newHashMap();
        buildExtAttr(ctx, extAttr, productM, productPinPoolM);
        HashMap<String, Object> extra = Maps.newHashMap();
        extra.put("productId", productM.getProductId());
        extra.put("selectDate", productPinPoolM.getBookStartTime());
        extra.put("skuId", productPinPoolM.getProductItemId());
        extra.put("poolId", productPinPoolM.getPoolId());
        extAttr.put("extra", JsonCodec.encode(extra));
        return JsonCodec.encode(extAttr);
    }

    private void buildExtAttr(ActivityContext ctx, Map<String, Object> extAttr, ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildExtAttr(ActivityContext,Map,ProductM,ProductPinPoolM)");
        if (POOL_FAILED.contains(productPinPoolM.getPoolStatus())) {
            return;
        }
        if (PoolStatusEnum.POOLING.getCode() == productPinPoolM.getPoolStatus() && !productPinPoolM.isCanJoin()) {
            return;
        }
        if (CollectionUtils.isEmpty(productPinPoolM.getPoolGroups())) {
            return;
        }
        if (productPinPoolM.getPoolGroups().stream().filter(Objects::nonNull).noneMatch(pinPoolGroup -> pinPoolGroup.getOrderFrom() == ON_LINE)) {
            return ;
        }
        extAttr.put("shareTitle", buildShareTitle(productM, productPinPoolM));
        extAttr.put("shareContext", buildShareContext(productPinPoolM));
        extAttr.put("shareUrl", buildShareUrl(ctx, productM,productPinPoolM));
    }

    private String buildShareUrl(ActivityContext ctx, ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildShareUrl(ActivityContext,ProductM,ProductPinPoolM)");
        int spuType = ParamsUtil.getIntSafely(ctx, QueryFetcher.Params.spuType);
        long dpPoiId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        ShopM shopM = Optional.ofNullable((ShopM) ctx.getParameters().get(ShelfActivityConstants.Ctx.ctxShop)).orElse(new ShopM());
        int dpCityId = shopM.getCityId() == 0 ? ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.dpCityId) : shopM.getCityId();
        List<ProductPinPoolGroupM> onLinePinPoolList = productPinPoolM.getPoolGroups().stream()
                .filter(pinPoolGroup -> pinPoolGroup.getOrderFrom() == ON_LINE)
                .collect(Collectors.toList());
        String unifiedOrderId = onLinePinPoolList.get(0).getUnifiedOrderId();
        // 实景和桌面分享链接不一样，美团和点评分享链接不一样。
        if (spuType == SPUTypeEnum.BackRoom.getCode()) {
            return UrlUtils.uriToUrl(buildPlatform(dpCityId), String.format(merchantMap.get("inactiveShareUrl"), dpPoiId, productM.getProductId(), unifiedOrderId));
        }
        if (spuType == SPUTypeEnum.DesktopRolePlayingGame.getCode()) {
            return UrlUtils.uriToUrl(buildPlatform(dpCityId), String.format(merchantMap.get("activeShareUrl"), productM.getProductId(), unifiedOrderId));
        }
        return EMPTY;
    }

    private int buildPlatform(int dpCityId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildPlatform(int)");
        return dpCityIdList.contains(dpCityId) ? VCPlatformEnum.DP.getType() : VCPlatformEnum.MT.getType();
    }

    private String buildShareContext(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildShareContext(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        int maxJoinPeople = productPinPoolM.getMaxNum() - productPinPoolM.getCurrentNum();
        if (POOL_SUCCESS.contains(productPinPoolM.getPoolStatus())) {
            return productPinPoolM.isCanJoin() ? String.format(merchantMap.get("canStart"), maxJoinPeople) : merchantMap.get("fullPeople");
        }
        int minJoinPeople = productPinPoolM.getMinNum() - productPinPoolM.getCurrentNum();
        if (minJoinPeople > 0) {
            return minJoinPeople == maxJoinPeople
                    ? String.format(merchantMap.get("needPeople"), minJoinPeople)
                    : String.format(merchantMap.get("needManyPeople"), minJoinPeople, maxJoinPeople);
        }
        return String.format(merchantMap.get("canStart"), maxJoinPeople);
    }

    private String buildShareTitle(ProductM productM, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildShareTitle(ProductM,ProductPinPoolM)");
        return String.format("%S(%S)", productM.getTitle(), buildPurchase(productPinPoolM.getBookStartTime()));
    }

    private List<DzPoolInfoVO> buildPoolInfoList(ActivityContext ctx, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildPoolInfoList(ActivityContext,ProductPinPoolM)");
        DzPoolInfoVO poolInfoVO = new DzPoolInfoVO();
        poolInfoVO.setUserList(buildProductUserM(productPinPoolM, ON_LINE));
        poolInfoVO.setOffLineUserList(buildProductUserM(productPinPoolM, OFF_LINE));
        poolInfoVO.setRemark(buildRemark(productPinPoolM));
        poolInfoVO.setPeopleNum(String.valueOf(productPinPoolM.getCurrentNum()));
        poolInfoVO.setMinNum(productPinPoolM.getMinNum());
        poolInfoVO.setMaxNum(productPinPoolM.getMaxNum());
        poolInfoVO.setCanJoin(buildCanJoin(ctx, productPinPoolM));
        poolInfoVO.setCurrentProgressDescStr(buildCurrentProgressDescStr(productPinPoolM));
        return Lists.newArrayList(poolInfoVO);
    }

    private String buildCurrentProgressDescStr(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildCurrentProgressDescStr(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        List<RichLabelVO> currentProgressList = buildCurrentProgressDesc(productPinPoolM);
        if (CollectionUtils.isEmpty(currentProgressList)) {
            return EMPTY;
        }
        // 给前端特殊处理，B端场景无法识别小写bold
        String currentProgressStr = JsonCodec.encode(currentProgressList).toLowerCase();
        return currentProgressStr.replace("bold", "Bold");
    }


    private String buildRemark(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildRemark(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        if (productPinPoolM.getPoolStatus() == PoolStatusEnum.POOL_SUCCESS.getCode() && !productPinPoolM.isCanJoin()) {
            return String.valueOf(1);
        }
        return EMPTY;
    }

    private boolean buildCanJoin(ActivityContext ctx, ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildCanJoin(ActivityContext,ProductPinPoolM)");
        // 实景不能编辑
        if (ParamsUtil.getIntSafely(ctx, QueryFetcher.Params.spuType) == SPUTypeEnum.BackRoom.getCode()) {
            return false;
        }
        if (productPinPoolM.getPoolStatus() == PoolStatusEnum.POOL_SUCCESS.getCode() && !productPinPoolM.isCanJoin()) {
            return productPinPoolM.getPoolGroups().stream().anyMatch(pinPoolGroup -> pinPoolGroup.getOrderFrom() == OFF_LINE);
        }
        if (POOLING_AND_SUCCESS.contains(productPinPoolM.getPoolStatus())) {
            return productPinPoolM.isCanJoin();
        }
        return false;
    }

    private List<RichLabelVO> buildCurrentProgressDesc(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildCurrentProgressDesc(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        if (POOL_FAILED.contains(productPinPoolM.getPoolStatus())) {
            return PinPoolInfoPaddingUtils.buildFailedPoolDesc(productPinPoolM);
        }
        if (POOL_SUCCESS.contains(productPinPoolM.getPoolStatus())) {
            return productPinPoolM.isCanJoin()
                    ? PinPoolInfoPaddingUtils.buildCanStartDesc(productPinPoolM.getMaxNum() - productPinPoolM.getCurrentNum())
                    : PinPoolInfoPaddingUtils.buildFullPeopleDesc();
        }
        if (PoolStatusEnum.POOLING.getCode() == productPinPoolM.getPoolStatus()) {
            int minJoinPeople = productPinPoolM.getMinNum() - productPinPoolM.getCurrentNum();
            int maxJoinPeople = productPinPoolM.getMaxNum() - productPinPoolM.getCurrentNum();
            if (minJoinPeople > 0) {
                return PinPoolInfoPaddingUtils.buildNotStartDesc(minJoinPeople,maxJoinPeople);
            }
            return PinPoolInfoPaddingUtils.buildCanStartDesc(maxJoinPeople);
        }
        return Lists.newArrayList();
    }

    private List<DzPoolUserVO> buildProductUserM(ProductPinPoolM pinPoolM, int orderFrom) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildProductUserM(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM,int)");
        if (CollectionUtils.isEmpty(pinPoolM.getPoolGroups())) {
            return Lists.newArrayList();
        }
        return pinPoolM.getPoolGroups().stream()
                .filter(productPinPoolGroupM -> productPinPoolGroupM.getOrderFrom() == orderFrom)
                .map(ProductPinPoolGroupM::getUsers)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(productUserM -> buildDzPoolUserVO(productUserM, orderFrom))
                .collect(Collectors.toList());
    }

    private DzPoolUserVO buildDzPoolUserVO(ProductUserM productUserM, int orderFrom) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildDzPoolUserVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductUserM,int)");
        DzPoolUserVO poolUserVO = new DzPoolUserVO();
        poolUserVO.setName(productUserM.getName());
        poolUserVO.setPlatform(orderFrom);
        poolUserVO.setGender(buildGender(productUserM.getSex()));
        poolUserVO.setAvatar(productUserM.getAvatar());
        return poolUserVO;
    }

    private int buildGender(String sex) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildGender(java.lang.String)");
        if (StringUtils.isBlank(sex) || SexEnum.DEFAULT.getName().equals(sex)) {
            return SexEnum.DEFAULT.getType();
        }
        return SexEnum.MALE.getName().equals(sex) ? SexEnum.MALE.getType() : SexEnum.FEMALE.getType();
    }

    private String buildDzTagName(String bookStartTime, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildDzTagName(java.lang.String,com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String players = productM.getAttr(SCPResourceAttrEnum.ATTR_ROLEPLAY_PLAYERS_MAN_AND_WOMAN.getKey());
        if (StringUtils.isNotBlank(players)) {
            return String.format("%s | %s", bookStartTime, players);
        }
        return bookStartTime;
    }

    private List<DzTagVO> buildAidDecisionTags(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildAidDecisionTags(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        if (PoolStatusEnum.POOLING.getCode() == productPinPoolM.getPoolStatus()) {
            if (!productPinPoolM.isCanJoin()){
                // 状态为在拼中 但无法加入
                return  Lists.newArrayList(PinPoolInfoPaddingUtils.buildNoStockTag());
            }
            int minJoinPeople = productPinPoolM.getMinNum() - productPinPoolM.getCurrentNum();
            if (minJoinPeople > 0) {
                boolean afterNow = new DateTime(productPinPoolM.getBookStartTime()).minusHours(6).isAfterNow();
                return afterNow ? Lists.newArrayList(PinPoolInfoPaddingUtils.buildLackOfPeopleTag())
                        : Lists.newArrayList(PinPoolInfoPaddingUtils.buildFailureTag());
            }
            return Lists.newArrayList(PinPoolInfoPaddingUtils.buildCanStartTag());
        }
        if (POOL_SUCCESS.contains(productPinPoolM.getPoolStatus())) {
            return !productPinPoolM.isCanJoin() ? Lists.newArrayList(PinPoolInfoPaddingUtils.buildFullPeopleTag())
                    : Lists.newArrayList(PinPoolInfoPaddingUtils.buildCanStartTag());
        }
        return Lists.newArrayList();
    }

    private int buildButtonStatus(ProductPinPoolM productPinPoolM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildButtonStatus(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM)");
        if (POOL_FAILED.contains(productPinPoolM.getPoolStatus())) {
            return UN_CAN_SHARE;
        }
        if (CollectionUtils.isEmpty(productPinPoolM.getPoolGroups())) {
            return UN_CAN_SHARE;
        }
        if (productPinPoolM.getPoolGroups().stream().filter(Objects::nonNull).noneMatch(pinPoolGroup -> pinPoolGroup.getOrderFrom() == ON_LINE)) {
            return UN_CAN_SHARE;
        }
        // 已流车/库存售罄不可分享
        if (PoolStatusEnum.POOLING.getCode() == productPinPoolM.getPoolStatus()) {
            return productPinPoolM.isCanJoin() ? CAN_SHARE : UN_CAN_SHARE;
        }
        return CAN_SHARE;
    }

    private String buildPurchase(long bookStartTime) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildPurchase(long)");
        if (bookStartTime == 0) {
            return EMPTY;
        }
        DateTime startTime = new DateTime(bookStartTime);
        return String.format(PURCHASE, startTime.toString("MM.dd"), buildPurchaseWeek(startTime, new DateTime()), startTime.toString("HH:mm"));
    }

    private String buildPurchaseWeek(DateTime currentDate, DateTime now) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability.MerchantPinListProductVoBuilder.buildPurchaseWeek(org.joda.time.DateTime,org.joda.time.DateTime)");
        if (currentDate.withTimeAtStartOfDay().equals(now.withTimeAtStartOfDay())) {
            return "今日";
        }
        if (currentDate.withTimeAtStartOfDay().equals(now.plusDays(1).withTimeAtStartOfDay())) {
            return "明日";
        }
        return WeekEnum.getDesc(currentDate.getDayOfWeek());
    }
}
