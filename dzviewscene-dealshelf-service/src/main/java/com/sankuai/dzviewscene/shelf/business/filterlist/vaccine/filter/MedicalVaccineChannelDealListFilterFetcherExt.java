package com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.Query;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;

import java.util.*;

/**
 * Created by zhaizhui on 2021/1/7
 */
@ExtPointInstance(name = "疫苗频道页筛选组件扩展点")
public class MedicalVaccineChannelDealListFilterFetcherExt extends AbstractConfigFilterFetcherExt {

    @Override
    public Map<Long, Query> loadQuery(ActivityContext activityContext, String groupName, FilterM filter) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter.MedicalVaccineChannelDealListFilterFetcherExt.loadQuery(ActivityContext,String,FilterM)");
        Map<Long, Query> filterToQuery = new HashMap<>();
        int filterid = Optional.ofNullable((Integer) activityContext.getParam(FilterListActivityConstants.FilterParams.filterId)).orElse(0);
        if (filterid > 0) {
            return null;
        }
        for (FilterBtnM filterBtnM : filter.getFilters()) {
            filterToQuery.put(filterBtnM.getFilterId(), buildQuery(filterBtnM.getFilterId(), filterid));
        }
        return filterToQuery;
    }

    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter.MedicalVaccineChannelDealListFilterFetcherExt.endIntercept(ActivityContext,String,FilterM)");
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return;
        }
        //根据筛选项下团单的个数倒排
        Collections.sort(filterM.getFilters(), (a, b) -> compareProductsSize(a.getProducts(), b.getProducts()));
    }

    /**
     * 比较筛选项下团单的个数
     */
    private int compareProductsSize(List<ProductM> a, List<ProductM> b) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter.MedicalVaccineChannelDealListFilterFetcherExt.compareProductsSize(java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(a) && CollectionUtils.isEmpty(b)) return 0;
        if (CollectionUtils.isNotEmpty(a) && CollectionUtils.isNotEmpty(b)) return b.size() - a.size();
        if (CollectionUtils.isNotEmpty(a)) return 1;
        return -1;
    }

    private SimplexQuery buildQuery(long secondFilterId, int filterid) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter.MedicalVaccineChannelDealListFilterFetcherExt.buildQuery(long,int)");

        return new SimplexQuery() {
            @Override
            public int topN(QueryContext context) {
                return 10;
            }
            @Override
            public boolean match(QueryContext context, ProductM product) {
                boolean currentLocationCity = BooleanUtils.toBoolean(product.getAttr("currentLocationCity"));
                long secondTabId = NumberUtils.toLong(product.getAttr("secondTabId"));
                long firstTabId = NumberUtils.toLong(product.getAttr("firstTabId"));
                return filterid == 0 && currentLocationCity && (secondTabId == secondFilterId || firstTabId == secondFilterId);
            }
            @Override
            public Comparator<ProductM> comparator(QueryContext context) {
                return (o1, o2) -> {
                    boolean currentLocationCityO1 = BooleanUtils.toBoolean(o1.getAttr("currentLocationCity"));
                    boolean currentLocationCityO2 = BooleanUtils.toBoolean(o2.getAttr("currentLocationCity"));
                    if (currentLocationCityO1 && !currentLocationCityO2) {
                        return -1;
                    }
                    if (!currentLocationCityO1 && currentLocationCityO2) {
                        return 1;
                    }
                    int aDisplaySale = o1.getSale() == null ? 0 : o1.getSale().getSale();
                    int bDisplaySale = o2.getSale() == null ? 0 : o2.getSale().getSale();
                    return bDisplaySale - aDisplaySale;
                };
            }
        };
    }

}
