package com.sankuai.dzviewscene.shelf.business.shelf.bathdeal.builder;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2020/9/2 12:22 下午
 */
@ExtPointInstance(name = "洗浴店内服务货架筛选栏构造扩展点")
public class BathServiceDealFilterBuilderExt extends FilterBuilderExtAdapter {

    /**
     * 筛选组件样式
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int showType(ActivityContext activityContext, String productType) {
        return 2;
    }

    /**
     * 筛选标签最小展示数
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        return 3;
    }

    /**
     * 子标签最小展示数
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String productType) {
        return 3;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM, int layer, int index) {
        Map<String, Object> labsMap = Maps.newHashMap();
        labsMap.put("title", filterBtnM.getTitle());
        labsMap.put("index", index);
        return JsonCodec.encodeWithUTF8(labsMap);
    }

    /**
     * 标题标签拼接
     *
     * @param activityContext
     * @param productType
     * @param filterBtnM
     * @return
     */
    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        return new RichLabelVO(13, "#111111", filterBtnM.getTitle());
    }

    @Override
    public RichLabelVO selectedTitleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(filterBtnM.getTitle());
        if(StringUtils.isNotEmpty(PerfectActivityBuildUtils.getActivityName()) && PerfectActivityBuildUtils.getActivityName().equals(filterBtnM.getTitle())) {
            richLabelVO.setTextColor(PerfectActivityBuildUtils.getSelectedFilterColor());
            if(PerfectActivityBuildUtils.getSelectedFilterSize() > 0) {
                richLabelVO.setTextSize(PerfectActivityBuildUtils.getSelectedFilterSize());
            }
            return richLabelVO;
        }
        return richLabelVO;
    }

}
