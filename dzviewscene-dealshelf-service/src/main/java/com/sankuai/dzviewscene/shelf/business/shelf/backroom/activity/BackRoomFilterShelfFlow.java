package com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 生成密室预订日期列表货架流程, 流程步骤如下:
 * 1. 第一步: 召回
 * 2. 第二步: 筛选
 * 3. 第三步: 填充
 * 4. 第四部: 校验/重新填充
 * <p>
 * 适应场景:
 * 密室日期列表场景
 */
@SuppressWarnings("unchecked")
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = BackRoomFilterShelfFlow.BACKROOM_FILTER_FLOW, name = "密室预订生成货架特殊流程")
public class BackRoomFilterShelfFlow implements IActivityFlow<ShelfGroupM> {
    public static final String BACKROOM_FILTER_FLOW = "BackroomFilterShelfFlow";

    @Override
    public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackRoomFilterShelfFlow.execute(AbstractActivity,ActivityContext)");
        // 1. 召回: 商品组名->商品列表
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCf = activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx);

        // 2. 召回之后生成筛选: 商品组名->生成筛选
        CompletableFuture<Map<String, FilterM>> filterMCf = productGroupsCf.thenCompose(productGroups -> {
            ctx.attach(FilterFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE).build(ctx);
        });

        // 3. 召回之后生成填充
        CompletableFuture<Map<String, ProductGroupM>> paddingResultCf = productGroupsCf.thenCompose(productGroups -> {
            ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
        });

        // 4. 筛选填充后校验日期,如果日期不一致则使用正确的日期重新填充
        CompletableFuture<Map<String, ProductGroupM>> finalPaddingResultCf = CompletableFuture.allOf(filterMCf, paddingResultCf).thenCompose(productGroups -> {
            if (validateSelectDate(ctx, filterMCf)) {
                return paddingResultCf;
            }
            return activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
        });

        return CompletableFuture.allOf(filterMCf, finalPaddingResultCf).thenApply(v -> {
            ShelfGroupM shelfGroupM = new ShelfGroupM();
            shelfGroupM.setFilterMs(filterMCf.join());
            shelfGroupM.setProductGroupMs(finalPaddingResultCf.join());
            return shelfGroupM;
        });
    }

    private boolean validateSelectDate(ActivityContext ctx, CompletableFuture<Map<String, FilterM>> filterMCf) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackRoomFilterShelfFlow.validateSelectDate(ActivityContext,CompletableFuture)");
        try {
            Map<String, FilterM> filterMMap = filterMCf.join();
            if (MapUtils.isEmpty(filterMMap) || filterMMap.get(ProductEnums.backroom.name()) == null) {
                return true;
            }

            FilterM filterM = filterMMap.get(ProductEnums.backroom.name());
            Date selectDate = ParamsUtil.getDateSafely(ctx, ShelfActivityConstants.Params.selectDate);
            //如果只有一个筛选则展示的是更多日期，此时不需要重新计算日期
            if (CollectionUtils.isEmpty(filterM.getFilters()) || filterM.getFilters().size() <= 1) {
                return true;
            }
            //如果传入的 selectDay为null，需要判断第一个筛选是不是展示今天日期，如果不是需要重新计算日期
            if (selectDate == null) {
                return !checkIsShowYesterday(ctx, filterM.getFilters());
            }
            return checkSelectDayInFilters(selectDate, ctx, filterM.getFilters());
        } catch (Exception e) {
            Cat.logEvent("validateSelectDateError", "checkSelectDayError");
        }
        return true;
    }

    private boolean checkIsShowYesterday(ActivityContext ctx, List<FilterBtnM> filters) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackRoomFilterShelfFlow.checkIsShowYesterday(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        if (!StringUtils.isNumeric(filters.get(0).getExtra())) {
            return false;
        }
        DateTime startOfToday = new DateTime().withTimeAtStartOfDay();
        DateTime firstBtnDate = new DateTime(Long.parseLong(filters.get(0).getExtra()));
        if (firstBtnDate.equals(startOfToday)) {
            return false;
        }
        ctx.getParameters().put(ShelfActivityConstants.Params.selectDate, firstBtnDate.toDate());
        return true;
    }

    //判断传入的 selectDay在不在当前筛选列表里，如果不在则重新填充 selectDay
    private boolean checkSelectDayInFilters(Date selectDate, ActivityContext ctx, List<FilterBtnM> filters) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackRoomFilterShelfFlow.checkSelectDayInFilters(java.util.Date,com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        DateTime startOfSelectDate = new DateTime(selectDate).withTimeAtStartOfDay();
        if (filters.stream().anyMatch(e -> matchDate(startOfSelectDate, e))) {
            return true;
        }
        ctx.getParameters().put(ShelfActivityConstants.Params.selectDate, new Date(Long.parseLong(filters.get(0).getExtra())));
        return false;
    }

    private boolean matchDate(DateTime selectDate, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackRoomFilterShelfFlow.matchDate(org.joda.time.DateTime,com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM)");
        if (!StringUtils.isNumeric(filterBtnM.getExtra())) {
            return false;
        }
        DateTime filterDate = new DateTime(Long.parseLong(filterBtnM.getExtra()));
        return selectDate.equals(filterDate.withTimeAtStartOfDay());
    }
}
