package com.sankuai.dzviewscene.shelf.business.shelf.baby.builder;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/1/12 8:07 下午
 */
@ExtPointInstance(name = "亲子-儿童乐园团购门票货架主标题模块构造扩展点")
public class BabyPlayGroundTicketShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.baby.play.ground.ticket.shelf.constant.config", defaultValue = "{}")
    public BabyPlayGroundTicketShelfConstant babyPlayGroundTicketShelfConstant;

    @Override
    public String title(ActivityContext activityContext) {
        return babyPlayGroundTicketShelfConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) {
            return babyPlayGroundTicketShelfConstant.getMtTitleIcon();
        }
        return babyPlayGroundTicketShelfConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        return Lists.newArrayList(buildReturnAnyTimeIcon(), buildReturnExpiredIcon());
    }

    private IconRichLabelVO buildReturnExpiredIcon() {
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setIcon(babyPlayGroundTicketShelfConstant.getTitleTagIcon());
        returnExpiredIcon.setText(new RichLabelVO(babyPlayGroundTicketShelfConstant.getOverTimeRefund()));
        return returnExpiredIcon;
    }

    private IconRichLabelVO buildReturnAnyTimeIcon() {
        IconRichLabelVO returnAnyTimeIcon = new IconRichLabelVO();
        returnAnyTimeIcon.setIcon(babyPlayGroundTicketShelfConstant.getTitleTagIcon());
        returnAnyTimeIcon.setText(new RichLabelVO(babyPlayGroundTicketShelfConstant.getAnyTimeRefund()));
        return returnAnyTimeIcon;
    }

    @Data
    private static class BabyPlayGroundTicketShelfConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
        private String anyTimeRefund;
        private String overTimeRefund;
    }
}

