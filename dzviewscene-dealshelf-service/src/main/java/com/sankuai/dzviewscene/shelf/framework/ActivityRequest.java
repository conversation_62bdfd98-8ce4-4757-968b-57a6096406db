package com.sankuai.dzviewscene.shelf.framework;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动执行引擎请求对象
 * <p>
 * Created by float.lu on 2020/8/20.
 */
@Data
public class ActivityRequest {

    /**
     * 活动标识
     */
    String activityCode;

    /**
     * 请求参数
     */
    private Map<String, Object> params = new HashMap<>();

    /**
     * trace日志开关标志
     */
    private static String traceMark = "_activity_trace";

    /**
     * 请求构造初始时间
     */
    private long startTime = System.currentTimeMillis();

    /**
     * 添加参数
     *
     * @param name
     * @param value
     */
    public void addParam(String name, Object value) {
        this.params.put(name, value);
    }

    public boolean isTrace() {
        return this.params.get(traceMark) != null;
    }
}
