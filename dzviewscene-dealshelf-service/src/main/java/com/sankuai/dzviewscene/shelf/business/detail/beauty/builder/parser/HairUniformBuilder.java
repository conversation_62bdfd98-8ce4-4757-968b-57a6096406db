package com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.parser;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentDataGroupUnitModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.DealSkuStructInfoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/18 2:29 下午
 */
public class HairUniformBuilder extends AbstructUniformBuilder {

    public static final String PRODUCT_OWNER = "productOwner";

    public static final String PRODUCT_OWNER_EX = "productOwnerEx";

    public static final String HAIRLEN = "hairLen";

    public static final String LENGTH_INSENSIVE = "hairLen";

    public static final String OTHER = "其他";

    public static final List<String> ALL_TYPE = Lists.newArrayList("洗剪吹", "快剪", "烫发", "护理", "染发", "洗吹", "欧莱雅", "菲灵");

    public static final List<String> PAINT_AND_BOIL = Lists.newArrayList("烫发","染发");

    private static final String ADDTION_PRICE_ATTR_NAME = "additionPrice";

    private static final String PRICE_ADDITION_STATE_FORMAT = "（过肩需加收%s元）";

    @Override
    public boolean test(ProductM productM) {
        UniformStructModel hairUniformStruct = getTemplate(productM);
        if(hairUniformStruct == null || hairUniformStruct.getContent() == null || CollectionUtils.isEmpty(hairUniformStruct.getContent())) {
            return false;
        }
        List<UniformStructContentModel> uniformStructContents = hairUniformStruct.getContent().stream().filter(content -> content.getType().equals(UNIFORM_STRUCTURE_TABLE)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(uniformStructContents)
                && getDealDetailCategory(productM).equals(HAIR_CATEGORY)) {
            return true;
        }
        return false;
    }

    @Override
    public DealSkuStructInfoVO buildDealSkuStructInfo(UniformStructContentDataGroupUnitModel unit, String serviceType) {
        String productName = getProductName(unit);
        String productOwner = getProductOwner(unit);
        String productPrice = getProductPrice(unit);
        String type = unit.getProjectName();
        String icon = getIconUrl(type, OTHER);
        if(productName == null) {
            return null;
        }
        if(PAINT_AND_BOIL.contains(type)) {
            String priceAdditionState = getPriceAddtionState(unit);
            if(StringUtils.isNotEmpty(priceAdditionState)) {
                return assembleDealSkuStructInfo(productName, productName + productOwner + getHairlen(unit) + priceAdditionState, unit.getAmount(), productPrice, icon);
            }
            return assembleDealSkuStructInfo(productName, productName + productOwner + getHairlen(unit), unit.getAmount(), productPrice, icon);
        }
        return assembleDealSkuStructInfo(productName, getDesc(unit), unit.getAmount(), productPrice, icon);
    }

    private String getPriceAddtionState(UniformStructContentDataGroupUnitModel unit) {
        if(MapUtils.isEmpty(unit.getAttrValues())) {
            return null;
        }
        String priceAddition = unit.getAttrValues().get(ADDTION_PRICE_ATTR_NAME);
        if(StringUtils.isEmpty(priceAddition)) {
            return null;
        }
        return String.format(PRICE_ADDITION_STATE_FORMAT, unit.getAttrValues().get(ADDTION_PRICE_ATTR_NAME));
    }

    private String getProductName(UniformStructContentDataGroupUnitModel unit) {
        if (unit == null) {
            return null;
        }
        String productNameEx = StringUtils.EMPTY;
        if (MapUtils.isNotEmpty(unit.getAttrValues())) {
            productNameEx = unit.getAttrValues().getOrDefault(PRODUCT_NAME_EX, StringUtils.EMPTY);
        }
        return OTHER.equals(unit.getProjectName()) ? productNameEx : unit.getProjectName();
    }

    private String getProductOwner(UniformStructContentDataGroupUnitModel unit) {
        String productOwner = StringUtils.EMPTY;
        String productOwnerEx = StringUtils.EMPTY;
        if (unit != null && MapUtils.isNotEmpty(unit.getAttrValues())) {
            productOwner = unit.getAttrValues().getOrDefault(PRODUCT_OWNER, StringUtils.EMPTY);
            productOwnerEx = unit.getAttrValues().getOrDefault(PRODUCT_OWNER_EX, StringUtils.EMPTY);
        }
        if (OTHER.equals(productOwner) || OTHER.equals(unit.getProjectName())) {
            productOwner = productOwnerEx;
        }
        return productOwner;
    }

    private String getDesc(UniformStructContentDataGroupUnitModel unit) {
        String desc = StringUtils.EMPTY;
        if (unit != null && MapUtils.isNotEmpty(unit.getAttrValues())) {
            desc = unit.getAttrValues().getOrDefault(PRODUCT_DESC, StringUtils.EMPTY);
        }
        if (StringUtils.isEmpty(desc)) {
            desc = getProductOwner(unit);
        }
        return desc;
    }

    private String getHairlen(UniformStructContentDataGroupUnitModel unit) {
        String harlen = StringUtils.EMPTY;
        if (unit != null && MapUtils.isNotEmpty(unit.getAttrValues())) {
            harlen = unit.getAttrValues().getOrDefault(HAIRLEN, StringUtils.EMPTY);
        }
        if (harlen.equals(LENGTH_INSENSIVE)) {
            harlen = StringUtils.EMPTY;
        }
        return harlen;
    }

    private String getProductPrice(UniformStructContentDataGroupUnitModel unit) {
        return String.valueOf(unit.getAmount() * unit.getPrice());
    }



}
