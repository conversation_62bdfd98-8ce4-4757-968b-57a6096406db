package com.sankuai.dzviewscene.shelf.framework.timeout;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.athena.inf.AthenaInf;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

public class TimeoutFuture<T> extends CompletableFuture<T> {

    private static final ExecutorService executorService = AthenaInf.getThreadPool("timeout_task_executor");

    private static final String MAIN_SWITCH = "com.sankuai.athenainf.timeout.main.switch";

    private static final String METRIC_SWITCH = "com.sankuai.athenainf.timeout.metric.switch";

    private static final String TOTAL_COUNT = "total_count";

    private static final String EVENT_NAME = "athena_timeout";

    private static final String EMPTY_COMPLETABLE_FUTURE = "completable_future_is_null";

    private long timeoutTime;

    private CompletableFuture<T> completableFuture;

    private TimeoutFuture() {
    }

    public TimeoutFuture(CompletableFuture<T> completableFuture) {
        if (completableFuture == null) {
            return;
        }
        this.completableFuture = completableFuture;
        completableFuture.whenComplete((v, e) -> {
            if (e != null) {
                this.completeExceptionally(e);
                return;
            }
            this.complete(v);
        });
    }

    public static <T> CompletableFuture<T> of(CompletableFuture<T> completableFuture, long timeout, TimeUnit timeUnit, T defaultValue) {
        if (completableFuture == null) {
            logMetric(EMPTY_COMPLETABLE_FUTURE);
            return CompletableFuture.completedFuture(defaultValue);
        }
        TimeoutFuture<T> timeoutFuture = new TimeoutFuture<>();
        timeoutFuture.completableFuture = completableFuture;
        timeoutFuture.onTimeoutWithDefault(timeout, timeUnit, defaultValue);
        return completableFuture;
    }

    public TimeoutFuture<T> onTimeoutWithDefault(long timeout, TimeUnit timeUnit, T defaultValue) {
        if (allowTimeout()) {
            logMetric(TOTAL_COUNT);
            this.timeoutTime = System.currentTimeMillis() + timeout;
            whenComplete(new Canceller(Delayer.delay(new DelayedCompleter<T>(this, defaultValue), timeout, timeUnit)));
        }
        return this;
    }

    static final class Canceller implements BiConsumer<Object, Throwable> {
        final Future<?> f;

        Canceller(Future<?> f) {
            this.f = f;
        }

        public void accept(Object ignore, Throwable ex) {
            if (ex == null && f != null && !f.isDone())
                f.cancel(false);
        }
    }

    static final class Delayer {
        static ScheduledFuture<?> delay(Runnable command, long delay, TimeUnit timeUnit) {
            return delayer.schedule(command, delay, timeUnit);
        }

        static final class DaemonThreadFactory implements ThreadFactory {
            private static final AtomicInteger threadNum = new AtomicInteger(1);

            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                t.setDaemon(true);
                t.setName("TimeoutFutureDelayer-thread-" + threadNum.getAndIncrement());
                return t;
            }
        }

        static final ScheduledExecutorService delayer;

        static {
            ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), new Delayer.DaemonThreadFactory());
            scheduledThreadPoolExecutor.setRemoveOnCancelPolicy(true);
            delayer = TraceExecutors.getTraceScheduledExecutorService(scheduledThreadPoolExecutor);
        }
    }

    static final class Timeout implements Runnable {
        final TimeoutFuture<?> f;

        Timeout(TimeoutFuture<?> f) {
            this.f = f;
        }

        public void run() {
            if (f != null && f.completableFuture != null && !f.completableFuture.isDone()) {
                logMetric(loadDiffRanges(f.timeoutTime));
                executorService.submit(() -> {
                    f.completeExceptionally(new TimeoutException());
                    f.completableFuture.completeExceptionally(new TimeoutException());
                });
            }
        }
    }

    static final class DelayedCompleter<U> implements Runnable {
        final TimeoutFuture<U> f;
        final U u;

        DelayedCompleter(TimeoutFuture<U> f, U u) {
            this.f = f;
            this.u = u;
        }

        public void run() {
            if (f != null && f.completableFuture != null && !f.completableFuture.isDone()) {
                logMetric(loadDiffRanges(f.timeoutTime));
                executorService.submit(() -> {
                    f.complete(u);
                    f.completableFuture.complete(u);
                });
            }
        }
    }

    private boolean allowTimeout() {
        return !this.isDone() && Lion.getBooleanValue(MAIN_SWITCH, true);
    }

    private static void logMetric(String metric) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutFuture.logMetric(java.lang.String)");
        if (!Lion.getBooleanValue(METRIC_SWITCH, true)) {
            return;
        }
        Cat.logEvent(EVENT_NAME, metric);
    }

    private static String loadDiffRanges(long timeoutTime) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutFuture.loadDiffRanges(long)");
        long diffMillis = System.currentTimeMillis() - timeoutTime;
        if (diffMillis <= 10) {
            return "<=10";
        }
        if (diffMillis <= 50) {
            return "<=50";
        }
        return ">50";
    }

}
