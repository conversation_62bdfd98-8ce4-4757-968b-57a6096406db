package com.sankuai.dzviewscene.shelf.business.shelf.bathdeal.builder;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2020/9/2 12:23 下午
 */
@ExtPointInstance(name = "洗浴货架打点信息构造扩展点")
public class BathDealOceanBuilderExt extends OceanBuilderExtAdapter {

    @Override
    public String filterBarLabs(ActivityContext activityContext) {
        return buildLabs(activityContext);
    }

    @Override
    public String moreLabs(ActivityContext activityContext) {
        return buildLabs(activityContext);
    }

    @Override
    public String wholeShelfLabs(ActivityContext activityContext) {
        return buildLabs(activityContext);
    }

    private String buildLabs(ActivityContext activityContext) {
        Map<String, Object> labs = new HashMap<>();
        labs.put("poi_id", getShopId(activityContext));
        labs.put("trade_title", getGroupName(activityContext));
        return JsonCodec.encode(labs);
    }

    private String getGroupName(ActivityContext activityContext) {
        List<String> groupNames = activityContext.getParam(QueryFetcher.Params.groupNames);
        if (CollectionUtils.isEmpty(groupNames)) {
            return "";
        }
        return groupNames.get(0);
    }

    private Integer getShopId(ActivityContext activityContext) {
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.mtPoiId) + "", 0);
        }
        return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.dpPoiId) + "", 0);
    }
}