package com.sankuai.dzviewscene.shelf.business.shelf.medical.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaizhui on 2020/10/27
 */
public abstract class AbstractFloorsBuilderExtAdapter extends AbstractDefaultFloorsBuilderExt {

    private FloorConstant floorConstant;

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter.floorDefaultShowNum(ActivityContext,String,List)");
        return floorConstant.defaultShowProductCount;
    }

    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter.itemComponentPurchase(ActivityContext,String,ProductM)");
        if (StringUtils.isBlank(productM.getPurchase())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(productM.getPurchase());
        return richLabelVO;
    }

    @Override
    public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        return productM.getTitle();
    }

    @Override
    public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter.itemComponentSale(ActivityContext,String,ProductM)");
        if(productM.getSale() == null || StringUtils.isBlank(productM.getSale().getSaleTag())) return Strings.EMPTY;
        return productM.getSale().getSaleTag();
    }

    @Override
    public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("deal_id", productM.getProductId());
        oceanMap.put("index", index);
        addPromoInfo(oceanMap, itemComponentPromoTags(activityContext, groupName, productM));
        return JsonCodec.encode(oceanMap);
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RichLabel {
        private String text;
        private String textcolor;
        private int textsize;
        private String textstyle;

        public RichLabel() {}

        public RichLabel(String text, String textcolor, int textsize) {
            this.text = text;
            this.textcolor =textcolor;
            this.textsize = textsize;
        }

        public RichLabel(String text, String textcolor, int textsize, String textstyle) {
            this.text = text;
            this.textcolor =textcolor;
            this.textsize = textsize;
            this.textstyle = textstyle;
        }

    }

    @Data
    static class FloorConstant {
        String title;
        String dpTitleIcon;
        String mtTitleIcon;
        String titleTagIcon;
        int front11;
        String color777777;
        String colorFF6633;
        String titleFormat;
        String boldFontWeight;
        double activityPicAspectRadio;
        double headerPicAspectRadio;
        int defaultShowProductCount;
        List<Long> titleFormatFilterIds;
        String morePrefix;
        String moreSuffix;
        int picWidth;
        int picHeight;
        int position;
    }
}
