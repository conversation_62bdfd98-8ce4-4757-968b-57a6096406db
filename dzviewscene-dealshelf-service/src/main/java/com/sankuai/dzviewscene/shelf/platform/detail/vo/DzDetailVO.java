package com.sankuai.dzviewscene.shelf.platform.detail.vo;

import com.sankuai.dzviewscene.shelf.platform.detail.vo.enums.ProductStatusEnums;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/4
 */
@Data
@Builder
public class DzDetailVO implements Serializable {

    /**
     * 商品状态 @see {@link ProductStatusEnums}
     */
    private int status;

    /**
     * 收藏状态，0：未收藏，1：收藏
     */
    private int collectStatus;

    /**
     * 按钮状态，0:正常，1：下线，2：不可购买（比如海外商户）
     */
    private int buttonStatus;

    /**
     * 基础信息模块
     */
    private DzBaseVO base;

    /**
     * 商品介绍模块
     */
    private DzDetailInfoVO detailInfo;

    /**
     * 保障详情
     */
    private GuaranteeVO guaranteeDetail;

    /**
     * 价格展示模块
     */
    private DzPriceVO price;

    /**
     * 适用门店模块
     */
    private DzAvailableShopVO availableShop;

    /**
     * 资源列表
     */
    private List<DzStructItemVO> resources;

    /**
     * 购买须知模块
     */
    private DzBuyRuleVO buyRule;

    /**
     * 结构化
     */
    private DzStructDetailVO struct;

    /**
     * 商品列表
     */
    private DzItemsVO items;

    /**
     * 额外费用
     */
    private DzStructItemVO extraCharge;

    /**
     * 服务流程
     */
    private DzStructItemVO serviceProcess;

    /**
     * 提示信息
     */
    private DzStructItemVO tipInfo;

    /**
     * 免费福利
     */
    private DzStructItemVO freeBenefits;

    /**
     * 图文信息
     */
    private List<DzPicTextVO> picTexts;

    /**
     * 活动模块
     */
    private DzActivityVO activity;

    /**
     * 扩展信息
     */
    private Map<String, Object> extAttrs;

}
