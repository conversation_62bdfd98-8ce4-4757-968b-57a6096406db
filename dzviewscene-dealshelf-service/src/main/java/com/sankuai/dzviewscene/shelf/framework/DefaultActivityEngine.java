package com.sankuai.dzviewscene.shelf.framework;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.dianping.pigeon.remoting.common.exception.RejectedException;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.onelimiter.LimitResult;
import com.dianping.rhino.onelimiter.OneLimiter;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.inf.rpc.timeout.exquisite.RpcTimeoutClient;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.dzviewscene.product.PmfAdaptationEngine;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.shelf.framework.core.IActivity;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityValidator;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.framework.exception.SceneIdentifyException;
import com.sankuai.dzviewscene.shelf.framework.monitor.ActivityMonitor;
import com.sankuai.dzviewscene.shelf.framework.monitor.DefaultActivityMonitor;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutCompletableFuture;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutFuture;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 活动执行引擎实现
 * <p>
 * Created by float.lu on 2020/8/21.
 */
@Component
public class DefaultActivityEngine implements ActivityEngine {

    private static final String NAME = "执行引擎";

    private static final ActivityMonitor activityMonitor = new DefaultActivityMonitor();

    private static final Logger logger = LoggerFactory.getLogger(DefaultActivityEngine.class);

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.framework.log.switch", defaultValue = "false")
    private boolean logSwitch;

    @Resource
    private ComponentFinder componentFinder;

    @Resource
    private PmfAdaptationEngine pmfAdaptationEngine;

    private OneLimiter pmfLimiter = Rhino.newOneLimiter("old-pmf-limiter");

    @Override
    public <T> ActivityResponse<T> execute(ActivityRequest activityRequest) {
        if (activityRequest == null) {
            throw new IllegalArgumentException("activityRequest 不能为空");
        }
        ActivityResponse activityResponse = new ActivityResponse();

        try {
            // 1. 构造活动上下文
            ActivityContext activityContext = componentFinder.findContextBuilder(activityRequest.getActivityCode()).build(activityRequest);

            // 2.新引擎分流
            if (pmfAdaptationEngine.usePmfEngine(activityContext)) {
                return pmfAdaptationEngine.executeWithPmfEngine(activityContext);
            }
            LogUtils.markTemplateFlow();
            // 3. 查找活动实例，Template流程使用
            IActivity activity = componentFinder.findActivity(activityRequest.getActivityCode());

            // 4. 设置活动ID
            activityContext.setActivityCode(activityRequest.getActivityCode());

            // 5. 设置请求开始时间
            activityContext.setStartTime(activityRequest.getStartTime());

            // 6. 识别场景ID
            String sceneCode = componentFinder.findSceneIdentifier(activityRequest.getActivityCode()).identify(activityContext);

            // 7. 设置场景ID
            setSceneTimeout(sceneCode);

            // 8. 设置场景ID
            activityContext.setSceneCode(sceneCode);

            if (isReject(activityContext)) {
                throw new RejectedException("触发场景限流器, 场景:" + activityContext.getActivityCode() + "." + activityContext.getSceneCode());
            }

            setSceneCode2ParamsIfNull(sceneCode, activityContext.getParameters());

            // 9. 填充附加参数
            componentFinder.findTemplate(activityContext).extParams(activityContext.getParameters());

            //////////////////////校验活动和设置扩展上下文信息为活动通用逻辑, 因此提取至此避免活动内部重复实现
            // 10. 校验活动
            if (!activityIsValid(activityContext)) {
                Cat.logEvent("InValid", sceneCode, "0", JsonCodec.encode(activityContext));
                return activityResponse;
            }

            // 11. 设置扩展上下文信息
            setExtContext(activityContext);

            // 12. 执行活动 + 构造结果
            ActivityResponse result = buildActivityResult(activityResponse, activityContext,
                    activityMonitor.monitor(activityContext, activity.execute(activityContext)));

            //13. Pmf Diff结果
            if (pmfAdaptationEngine.shouldDiff(sceneCode)) {
                pmfAdaptationEngine.diffWithPmfEngine(activityRequest, result);
            }
            return result;
        } catch (Exception e) {
            if (e instanceof SceneIdentifyException) {
                logSceneIdentifyError(e);
                // 给结果赋空对象，避免前端并发调用时报450错误
                activityResponse.setResult(CompletableFuture.completedFuture(new Object()));
            } else {
                // 12. 添加打点
                doTrace(activityRequest, activityResponse, "EngineError", null, e);
                Cat.logErrorWithCategory("EngineError", JsonCodec.encode(activityRequest), e);
            }
        } finally {
            //13. 清空本次调用的超时控制信息
            RpcTimeoutClient.clear();
        }
        return activityResponse;
    }

    private boolean isReject(ActivityContext context) {
        try {
            String entrance = context.getActivityCode() + "." + context.getSceneCode();
            Map<String, String> params = Maps.newHashMap();
            params.put("sceneCode", context.getSceneCode());
            LimitResult limitResult = pmfLimiter.run(entrance, params);
            if (limitResult.isReject()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            // no operation needed
        }
        return false;
    }


    private void logSceneIdentifyError(Exception throwable) {
        try {
            Cat.logEvent("SceneIdentifyError", "ERROR");
            if (logSwitch) {
                logger.info(XMDLogFormat.build()
                        .putTag("scene", " DefaultActivityEngine")
                        .putTag("method", "logSceneIdentifyError")
                        .message(throwable.getMessage()));
            }
        } catch (Exception e) {
            //静默
        }
    }

    private void setSceneCode2ParamsIfNull(String sceneCode, Map<String, Object> parameters) {
        if (parameters == null || StringUtils.isNotEmpty(ParamsUtil.getStringSafely(parameters, ShelfActivityConstants.Params.sceneCode))) {
            return;
        }
        parameters.put(ShelfActivityConstants.Params.sceneCode, sceneCode);
    }

    private void setSceneTimeout(String sceneCode) {
        //设置本次执行的所有Rpc调用限制时间
        RpcTimeoutClient.openRequestTimeout(TimeoutCompletableFuture.getTimeout(sceneCode));
    }


    private void doTrace(ActivityRequest activityRequest, ActivityResponse activityResponse, String name, Object result, Throwable throwable) {
        if (!activityRequest.isTrace()) {
            return;
        }
        activityResponse.addTraceElement(
                TraceElement.build(
                        NAME,
                        name,
                        null,
                        result,
                        throwable,
                        System.currentTimeMillis() - activityRequest.getStartTime()
                )
        );
    }


    // 构造获活动执行结果
    private <T> ActivityResponse buildActivityResult(ActivityResponse activityResponse, ActivityContext activityContext, CompletableFuture<T> result) {
        activityResponse.setResult(TimeoutCompletableFuture.of(activityContext.getSceneCode(), result));
        activityResponse.setTraceElements(activityContext.getTraceElements());
        return activityResponse;
    }


    // 校验活动
    private boolean activityIsValid(ActivityContext activityContext) {
        try {
            List<IActivityValidator> validators = componentFinder.findActivityValidators(activityContext);
            if (CollectionUtils.isEmpty(validators)) {
                return true;
            }
            List<IActivityValidator> unValidValidators = validators
                    .stream()
                    .filter(iActivityValidator -> !iActivityValidator.isValid(activityContext))
                    .collect(Collectors.toList());
            return CollectionUtils.isEmpty(unValidValidators);
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "DefaultActivityEngine","activityIsValid"), e);
            throw e;
        }
    }


    // 通过扩展点查询业务场景扩展上下文，这里会有序的填充contextExts，支持后面的context依赖前一个context的返回
    private void setExtContext(ActivityContext activityContext) {
        activityContext.setExtContext(Maps.newHashMap());
        List<IContextExt> contextExts = componentFinder.findContextExt(activityContext);
        if (CollectionUtils.isEmpty(contextExts)) {
            return;
        }
        contextExts.forEach(contextExt -> activityContext.getExtContext().put(contextExt.contextKey(), executeExtContext(contextExt, activityContext))
        );
    }

    private CompletableFuture<?> executeExtContext(IContextExt contextExt, ActivityContext activityContext) {
        if (TimeoutUtils.openTimeout()) {
            return TimeoutFuture.of(contextExt.contextExt(activityContext), TimeoutUtils.getRestTimeout(activityContext), TimeUnit.MILLISECONDS, null);
        }
        return contextExt.contextExt(activityContext);
    }

}
