package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class ButtonCarouselMsgStrategyFactoryTest {

    @Mock
    private ApplicationContext appCtx;

    private ButtonCarouselMsgStrategyFactory factory;

    @Before
    public void setUp() {
        factory = new ButtonCarouselMsgStrategyFactory();
        factory.setApplicationContext(appCtx);
    }

    /**
     * 测试 afterPropertiesSet 方法，当 appCtx.getBeansOfType 返回的 Map 为空时
     */
    @Test
    public void testAfterPropertiesSetWhenBeansOfTypeIsEmpty() throws Exception {
        // arrange
        when(appCtx.getBeansOfType(ButtonCarouselMsgStrategy.class)).thenReturn(new HashMap<>());
        // act
        factory.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(ButtonCarouselMsgStrategy.class);
    }

    /**
     * 测试 afterPropertiesSet 方法，当 appCtx.getBeansOfType 返回的 Map 不为空时
     */
    @Test
    public void testAfterPropertiesSetWhenBeansOfTypeIsNotEmpty() throws Exception {
        // arrange
        Map<String, ButtonCarouselMsgStrategy> beanMap = new HashMap<>();
        ButtonCarouselMsgStrategy strategy = mock(ButtonCarouselMsgStrategy.class);
        when(strategy.getName()).thenReturn("test");
        beanMap.put("test", strategy);
        when(appCtx.getBeansOfType(ButtonCarouselMsgStrategy.class)).thenReturn(beanMap);
        // act
        factory.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(ButtonCarouselMsgStrategy.class);
        verify(strategy, times(1)).getName();
    }
}
