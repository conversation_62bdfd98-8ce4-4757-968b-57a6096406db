package com.sankuai.dzviewscene.product.shelf.ability.builder.floors;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FloorHasNextVP;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class FloorsBuilderTest {

    @InjectMocks
    private FloorsBuilder floorsBuilder;

    @Mock
    private ComponentFinder componentFinder;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 buildHasNext 方法，当 findVPoint 返回 null 时，默认返回 false。
     */
    @Test
    public void testBuildHasNextWhenFindVPointReturnsNull() {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductGroupM currentFloorM = mock(ProductGroupM.class);

        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(null);

        boolean result = floorsBuilder.buildHasNext(activityCxt, currentFloorM);

        assertFalse("Expected default false when findVPoint returns null", result);
    }

    /**
     * 测试 buildHasNext 方法，当 floorHasNextVP.execute 返回 true 时。
     */
    @Test
    public void testBuildHasNextWhenExecuteReturnsTrue() {
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ProductGroupM currentFloorM = mock(ProductGroupM.class);
        FloorHasNextVP<?> floorHasNextVP = mock(FloorHasNextVP.class);

        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(floorHasNextVP);
        when(floorHasNextVP.execute(any(ActivityCxt.class), any(FloorHasNextVP.Param.class))).thenReturn(true);

        boolean result = floorsBuilder.buildHasNext(activityCxt, currentFloorM);

        assertTrue("Expected true when floorHasNextVP.execute returns true", result);
    }

    /**
     * 测试 findExtPoint 方法，正常情况
     */
    @Test
    public void testFindExtPointNormal() throws Throwable {
        // arrange
        ActivityContext ctx = new ActivityContext();
        String extCode = "testExtCode";
        FloorsBuilderExt expectedExtPoint = mock(FloorsBuilderExt.class);
        when(componentFinder.findExtPoint(ctx, extCode)).thenReturn(expectedExtPoint);
        // act
        FloorsBuilderExt actualExtPoint = floorsBuilder.findExtPoint(ctx, extCode);
        // assert
        assertEquals(expectedExtPoint, actualExtPoint);
    }

    /**
     * 测试 findExtPoint 方法，异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testFindExtPointException() throws Throwable {
        // arrange
        ActivityContext ctx = new ActivityContext();
        String extCode = "testExtCode";
        when(componentFinder.findExtPoint(ctx, extCode)).thenThrow(new RuntimeException());
        // act
        floorsBuilder.findExtPoint(ctx, extCode);
        // assert
        // Exception is expected
    }
}
