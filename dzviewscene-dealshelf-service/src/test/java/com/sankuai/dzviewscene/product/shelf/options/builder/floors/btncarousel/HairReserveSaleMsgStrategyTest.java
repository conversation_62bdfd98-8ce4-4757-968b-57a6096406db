package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.CarouselMsgTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class HairReserveSaleMsgStrategyTest {

    /**
     * Test context is null.
     */
    @Test
    public void testUnifiedBuild_ContextIsNull() throws Throwable {
        HairReserveSaleMsgStrategy strategy = new HairReserveSaleMsgStrategy();
        CarouselMsg result = strategy.unifiedBuild(null);
        Assert.assertNull(result);
    }

    /**
     * Test context's ProductM object is null.
     */
    @Test
    public void testUnifiedBuild_ProductMIsNull() throws Throwable {
        HairReserveSaleMsgStrategy strategy = new HairReserveSaleMsgStrategy();
        CarouselBuilderContext context = new CarouselBuilderContext();
        CarouselMsg result = strategy.unifiedBuild(context);
        Assert.assertNull(result);
    }

    /**
     * Test ProductM object's sale property is null.
     */
    @Test
    public void testUnifiedBuild_SaleIsNull() throws Throwable {
        HairReserveSaleMsgStrategy strategy = new HairReserveSaleMsgStrategy();
        CarouselBuilderContext context = new CarouselBuilderContext();
        ProductM productM = new ProductM();
        context.setProductM(productM);
        CarouselMsg result = strategy.unifiedBuild(context);
        Assert.assertNull(result);
    }

}
