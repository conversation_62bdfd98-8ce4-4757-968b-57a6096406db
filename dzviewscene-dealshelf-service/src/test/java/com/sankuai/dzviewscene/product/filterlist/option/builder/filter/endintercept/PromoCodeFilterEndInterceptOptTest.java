package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * PromoCodeFilterEndInterceptOpt 类的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoCodeFilterEndInterceptOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @InjectMocks
    private PromoCodeFilterEndInterceptOpt promoCodeFilterEndInterceptOpt;

    /**
     * 测试当配置的过滤Tab名称列表为空时
     */
    @Test
    public void testComputeWhenFilterTabNamesIsEmpty() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Collections.emptyList());

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当过滤列表为空时
     */
    @Test
    public void testComputeWhenFilterListIsEmpty() {
        // 准备测试数据
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(Collections.emptyList()).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当过滤列表为null时
     */
    @Test
    public void testComputeWhenFilterListIsNull() {
        // 准备测试数据
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(null).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当DzFilterVO为null时
     */
    @Test
    public void testComputeWhenDzFilterVOIsNull() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        filterList.add(null);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试当DzFilterVO的children为空时
     */
    @Test
    public void testComputeWhenChildrenIsEmpty() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        dzFilterVO.setChildren(Collections.emptyList());
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();


        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
    }

    /**
     * 测试正常情况下过滤Tab
     */
    @Test
    public void testComputeNormalCase() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();

        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn1 = new DzFilterBtnVO();
        btn1.setName("神券");
        DzFilterBtnVO btn2 = new DzFilterBtnVO();
        btn2.setName("秒杀");
        DzFilterBtnVO btn3 = new DzFilterBtnVO();
        btn3.setName("特团");
        DzFilterBtnVO btn4 = new DzFilterBtnVO();
        btn4.setName("其他");

        children.add(btn1);
        children.add(btn2);
        children.add(btn3);
        children.add(btn4);

        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size());
        assertEquals("其他", dzFilterVO.getChildren().get(0).getName());
    }

    /**
     * 测试嵌套子Tab的过滤
     */
    @Test
    public void testComputeWithNestedChildren() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();

        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn1 = new DzFilterBtnVO();
        btn1.setName("父Tab");

        List<DzFilterBtnVO> nestedChildren = new ArrayList<>();
        DzFilterBtnVO nestedBtn1 = new DzFilterBtnVO();
        nestedBtn1.setName("神券");
        DzFilterBtnVO nestedBtn2 = new DzFilterBtnVO();
        nestedBtn2.setName("其他子Tab");

        nestedChildren.add(nestedBtn1);
        nestedChildren.add(nestedBtn2);
        btn1.setChildren(nestedChildren);

        children.add(btn1);
        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        PromoCodeFilterEndInterceptOpt.Config config = new PromoCodeFilterEndInterceptOpt.Config();
        config.setFilterTabNames(Arrays.asList("神券", "秒杀", "特团"));

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, config);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size());
        assertEquals("父Tab", dzFilterVO.getChildren().get(0).getName());
        assertEquals(1, dzFilterVO.getChildren().get(0).getChildren().size());
        assertEquals("其他子Tab", dzFilterVO.getChildren().get(0).getChildren().get(0).getName());
    }

    /**
     * 测试当配置为null时
     */
    @Test
    public void testComputeWhenConfigIsNull() {
        // 准备测试数据
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO dzFilterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO btn = new DzFilterBtnVO();
        btn.setName("测试");
        children.add(btn);
        dzFilterVO.setChildren(children);
        filterList.add(dzFilterVO);
        FilterEndInterceptVP.Param param = FilterEndInterceptVP.Param.builder().filterList(filterList).build();

        // 执行测试
        Void result = promoCodeFilterEndInterceptOpt.compute(activityCxt, param, null);

        // 验证结果
        assertNull(result);
        assertEquals(1, dzFilterVO.getChildren().size()); // 不应该有任何变化
    }
}