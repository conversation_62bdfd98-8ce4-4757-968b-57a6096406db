package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.options.VRInterestFetcherOpt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class NursingCenterRoomItemOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private ProductM mockProductM;
    @Mock
    private ItemOceanLabsVP.Param param;
    private NursingCenterRoomItemOceanLabsOpt.Config config;
    private NursingCenterRoomItemOceanLabsOpt target;

    private MockedStatic<ProductMAttrUtils> productMAttrUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        target = new NursingCenterRoomItemOceanLabsOpt() {
            @Override
            protected Map<String, Object> getCommonOcean(Param param) {
                // 返回一个预设的Map，以便于测试
                return new HashMap<>();
            }

            @Override
            public void paddingDiscountAndComparePriceLab(Map<String, Object> source, ProductM productM) {
            }
        };
        config = new NursingCenterRoomItemOceanLabsOpt.Config();
        when(param.getProductM()).thenReturn(mockProductM);
        productMAttrUtils = mockStatic(ProductMAttrUtils.class);
    }

    @After
    public void tearDown() {
        productMAttrUtils.close();
    }

    /**
     * 测试正常场景，包括VR体验和顶部展示信息
     */
    @Test
    public void testComputeNormalScenario() throws Throwable {
        // arrange
        when(mockActivityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(true);
        when(mockProductM.getAttr(NursingCenterRoomItemOceanLabsOpt.VR_URL_ATTR)).thenReturn("someVrUrl");
        config.setEnableTopDisplayInfoLabs(true);
        NursingCenterRoomItemOceanLabsOpt.LabConfig labConfig = new NursingCenterRoomItemOceanLabsOpt.LabConfig();
        labConfig.setLabKey("topDisplay");
        labConfig.setLabValue("true");
        config.setTopDisplayLabConfig(labConfig);
        productMAttrUtils.when(()->ProductMAttrUtils.isTopDisplayProduct(mockProductM)).thenReturn(true);
        // act
        String result = target.compute(mockActivityCxt, param, config);

        // assert
        assertTrue(result.contains("\"hasVr\":true"));
        assertTrue(result.contains("\"topDisplay\":\"true\""));
    }

    /**
     * 测试VR权益不可用场景
     */
    @Test
    public void testComputeVrNotAvailable() throws Throwable {
        // arrange
        when(mockActivityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(false);
        when(mockProductM.getAttr(NursingCenterRoomItemOceanLabsOpt.VR_URL_ATTR)).thenReturn("someVrUrl");
        // act
        String result = target.compute(mockActivityCxt, param, config);

        // assert
        assertFalse(result.contains("\"hasVr\":true"));
    }

    /**
     * 测试顶部展示信息不可用场景
     */
    @Test
    public void testComputeNotTopDisplayProduct() throws Throwable {
        // arrange
        config.setEnableTopDisplayInfoLabs(true);
        NursingCenterRoomItemOceanLabsOpt.LabConfig labConfig = new NursingCenterRoomItemOceanLabsOpt.LabConfig();
        labConfig.setLabKey("topDisplay");
        labConfig.setLabValue("true");
        config.setTopDisplayLabConfig(labConfig);
        productMAttrUtils.when(()->ProductMAttrUtils.isTopDisplayProduct(mockProductM)).thenReturn(false);
        // act
        String result = target.compute(mockActivityCxt, param, config);
        // assert
        assertFalse(result.contains("\"topDisplay\":\"true\""));
    }

    /**
     * 测试顶部展示信息不可用场景
     */
    @Test
    public void testComputeNotTopDisplayInfoLabs() throws Throwable {
        // arrange
        config.setEnableTopDisplayInfoLabs(false);
        // act
        String result = target.compute(mockActivityCxt, param, config);
        // assert
        assertFalse(result.contains("\"topDisplay\":\"true\""));
    }
}
