package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupsAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GlassesSkuListGroupsAfterPricessingOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListGroupsAfterProcessingVP.Param param;

    @Mock
    private GlassesSkuListGroupsAfterPricessingOpt.Config config;

    private List<DealSkuGroupModuleVO> createSkuGroupsWithAllTypes() {
        DealSkuGroupModuleVO moduleVO = new DealSkuGroupModuleVO();
        moduleVO.setTitle("组1");
        DealSkuVO skuVO = new DealSkuVO();
        List<DealSkuItemVO> items = new ArrayList<>();
        items.add(createItemVO("镜片"));
        items.add(createItemVO("镜框"));
        items.add(createItemVO("配件"));
        items.add(createItemVO("增值服务"));
        skuVO.setItems(items);
        moduleVO.setDealSkuList(Lists.newArrayList(skuVO));
        return Lists.newArrayList(moduleVO);
    }

    private List<DealSkuGroupModuleVO> createMultipleSkuGroups() {
        List<DealSkuGroupModuleVO> groups = new ArrayList<>();
        DealSkuGroupModuleVO moduleVO1 = new DealSkuGroupModuleVO();
        moduleVO1.setTitle("组1");
        DealSkuVO skuVO1 = new DealSkuVO();
        skuVO1.setItems(Lists.newArrayList(createItemVO("镜片"), createItemVO("镜框"), createItemVO("配件"), createItemVO("增值服务")));
        moduleVO1.setDealSkuList(Lists.newArrayList(skuVO1));
        DealSkuGroupModuleVO moduleVO2 = new DealSkuGroupModuleVO();
        moduleVO2.setTitle("组2");
        DealSkuVO skuVO2 = new DealSkuVO();
        skuVO2.setItems(Lists.newArrayList(createItemVO("镜片"), createItemVO("镜框"), createItemVO("配件"), createItemVO("增值服务")));
        moduleVO2.setDealSkuList(Lists.newArrayList(skuVO2));
        groups.add(moduleVO1);
        groups.add(moduleVO2);
        return groups;
    }

    private DealSkuItemVO createItemVO(String name) {
        DealSkuItemVO itemVO = new DealSkuItemVO();
        itemVO.setName(name);
        return itemVO;
    }

    @Test
    public void testComputeSkuGroupsIsEmpty() throws Throwable {
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        when(param.getSkuGroups()).thenReturn(new ArrayList<>());
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        assertNotNull("Result should not be null", result);
        assertTrue("Result list should be empty when skuGroups is empty", result.isEmpty());
    }

    @Test
    public void testComputeDealSkuListIsNull() throws Throwable {
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        List<DealSkuGroupModuleVO> skuGroups = new ArrayList<>();
        skuGroups.add(new DealSkuGroupModuleVO());
        when(param.getSkuGroups()).thenReturn(skuGroups);
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        assertNotNull("Result should not be null", result);
        assertFalse("Result list should not be empty", result.isEmpty());
    }

    @Test
    public void testComputeItemsIsNull() throws Throwable {
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        List<DealSkuGroupModuleVO> skuGroups = new ArrayList<>();
        DealSkuGroupModuleVO skuGroupModuleVO = new DealSkuGroupModuleVO();
        skuGroupModuleVO.setDealSkuList(new ArrayList<>());
        skuGroups.add(skuGroupModuleVO);
        when(param.getSkuGroups()).thenReturn(skuGroups);
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        assertNotNull("Result should not be null", result);
        assertFalse("Result list should not be empty", result.isEmpty());
    }

    @Test
    public void testComputeNameIsNotJingpianOrJingkuangOrPeijianOrZengzhifuwu() throws Throwable {
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        List<DealSkuGroupModuleVO> skuGroups = new ArrayList<>();
        DealSkuGroupModuleVO skuGroupModuleVO = new DealSkuGroupModuleVO();
        DealSkuVO dealSkuVO = new DealSkuVO();
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName("other");
        dealSkuItemVO.setValue("100");
        dealSkuVO.setItems(new ArrayList<>());
        dealSkuVO.getItems().add(dealSkuItemVO);
        skuGroupModuleVO.setDealSkuList(new ArrayList<>());
        skuGroupModuleVO.getDealSkuList().add(dealSkuVO);
        skuGroups.add(skuGroupModuleVO);
        when(param.getSkuGroups()).thenReturn(skuGroups);
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        assertNotNull("Result should not be null", result);
        assertFalse("Result list should not be empty", result.isEmpty());
    }

    /**
     * Test compute method with all types of items
     */
    @Test
    public void testComputeWithAllItemTypes() throws Throwable {
        // arrange
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        ActivityCxt context = new ActivityCxt();
        GlassesSkuListGroupsAfterPricessingOpt.Config config = new GlassesSkuListGroupsAfterPricessingOpt.Config();
        List<DealSkuGroupModuleVO> skuGroups = createSkuGroupsWithAllTypes();
        List<AttrM> dealAttrs = Lists.newArrayList(new AttrM("product_name", "测试商品"));
        GlassesSkuListGroupsAfterPricessingOpt.Param param = GlassesSkuListGroupsAfterPricessingOpt.Param.builder().skuGroups(skuGroups).dealAttrs(dealAttrs).build();
        // act
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealSkuGroupModuleVO resultModule = result.get(0);
        assertNotNull(resultModule.getDealSkuList());
        assertEquals(1, resultModule.getDealSkuList().size());
        DealSkuVO resultSku = resultModule.getDealSkuList().get(0);
        List<DealSkuItemVO> items = resultSku.getItems();
        assertNotNull(items);
        assertEquals(4, items.size());
        assertEquals("测试商品", resultSku.getTitle());
    }

    // Additional test cases can be added here following the same pattern
    /**
     * Test compute method with multiple groups containing same item types
     */
    @Test
    public void testComputeWithMultipleGroups() throws Throwable {
        // arrange
        GlassesSkuListGroupsAfterPricessingOpt opt = new GlassesSkuListGroupsAfterPricessingOpt();
        ActivityCxt context = new ActivityCxt();
        GlassesSkuListGroupsAfterPricessingOpt.Config config = new GlassesSkuListGroupsAfterPricessingOpt.Config();
        List<DealSkuGroupModuleVO> skuGroups = createMultipleSkuGroups();
        GlassesSkuListGroupsAfterPricessingOpt.Param param = GlassesSkuListGroupsAfterPricessingOpt.Param.builder().skuGroups(skuGroups).build();
        // act
        List<DealSkuGroupModuleVO> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealSkuGroupModuleVO resultModule = result.get(0);
        assertNotNull(resultModule.getDealSkuList());
        assertEquals(1, resultModule.getDealSkuList().size());
        DealSkuVO resultSku = resultModule.getDealSkuList().get(0);
        List<DealSkuItemVO> items = resultSku.getItems();
        assertNotNull(items);
        assertEquals(8, items.size());
    }
}
