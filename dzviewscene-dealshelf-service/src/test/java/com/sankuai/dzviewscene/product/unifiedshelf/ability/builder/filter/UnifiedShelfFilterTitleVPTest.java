package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter;

import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterTitleVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Assert;
import org.junit.Test;
import com.sankuai.dzviewscene.dealshelf.shelfvo.IconRichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;

public class UnifiedShelfFilterTitleVPTest {

    @Test
    public void testProcessProductTotalNumNegative() throws Throwable {
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        Assert.assertNotNull(param1);
    }

    /**
     * 测试 buildImageRichLabModel 方法，输入正常的 PictureModel 实例
     */
    @Test
    public void testBuildImageRichLabModelWithValidPictureModel() {
        // arrange
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.5);
        pictureModel.setPicHeight(100);
        pictureModel.setPicUrl("http://example.com/pic.jpg");
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");
        param1.setFilterBtnM(filterBtnM1);
        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param1);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(pictureModel, result.getIcon());
        Assert.assertEquals(1, result.getType());
    }

    /**
     * 测试 buildImageRichLabModel 方法，输入为 null 的 PictureModel 实例
     */
    @Test
    public void testBuildImageRichLabModelWithNullPictureModel() {
        // arrange
        PictureModel pictureModel = null;
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");

        param1.setFilterBtnM(filterBtnM1);
        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param1);

        // assert
        Assert.assertNotNull(result);
        Assert.assertNull(result.getIcon());
        Assert.assertEquals(1, result.getType());
    }

    /**
     * 测试 buildImageRichLabModel 方法，输入的 PictureModel 实例包含空的 picUrl
     */
    @Test
    public void testBuildImageRichLabModelWithEmptyPicUrl() {
        // arrange
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.0);
        pictureModel.setPicHeight(200);
        pictureModel.setPicUrl("");
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        param1.setFilterBtnM(filterBtnM1);

        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param1);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(pictureModel, result.getIcon());
        Assert.assertEquals(1, result.getType());
    }

    /**
     * 测试 buildImageRichLabModel 方法，输入的 PictureModel 实例包含非法的 aspectRadio（负数）
     */
    @Test
    public void testBuildImageRichLabModelWithNegativeAspectRadio() {
        // arrange
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(-1.0);
        pictureModel.setPicHeight(300);
        pictureModel.setPicUrl("http://example.com/negative.jpg");
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().filterBtnM(filterBtnM1).build();

        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param1);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(pictureModel, result.getIcon());
        Assert.assertEquals(1, result.getType());
    }

    /**
     * 测试 buildImageRichLabModel 方法，输入的 PictureModel 实例包含非法的 picHeight（负数）
     */
    @Test
    public void testBuildImageRichLabModelWithNegativePicHeight() {
        // arrange
        PictureModel pictureModel = new PictureModel();
        pictureModel.setAspectRadio(1.0);
        pictureModel.setPicHeight(-100);
        pictureModel.setPicUrl("http://example.com/negativeHeight.jpg");
        UnifiedShelfFilterTitleVP.Param param1 = UnifiedShelfFilterTitleVP.Param.builder().build();
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setTitle("测试");
        param1.setFilterBtnM(filterBtnM1);

        // act
        IconRichLabelModel result = UnifiedShelfFilterTitleVP.buildImageRichLabModel(pictureModel, param1);

        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(pictureModel, result.getIcon());
        Assert.assertEquals(1, result.getType());
    }


}
