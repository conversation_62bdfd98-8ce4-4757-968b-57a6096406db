package com.sankuai.dzviewscene.product.shelf.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

public class DzTagHideUtilsTest {

    @Test
    public void testHideTagByDouhu() {
        DzTagVO dzTagVO = new DzTagVO();
        DzTagHideUtils.PerceptionHideInfo hideInfo = new DzTagHideUtils.PerceptionHideInfo();
        List<String > hideTestList = Lists.newArrayList("exp1_a");
        hideInfo.setHideTestList(hideTestList);
        List<DouHuM> douHuMs = Lists.newArrayList();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("exp1_a");
        douHuMs.add(douHuM);

        DzTagVO dzTagVO1 = DzTagHideUtils.hideTagByDouhu(dzTagVO, hideInfo, douHuMs);
        Assert.assertNull(dzTagVO1);
    }
}
