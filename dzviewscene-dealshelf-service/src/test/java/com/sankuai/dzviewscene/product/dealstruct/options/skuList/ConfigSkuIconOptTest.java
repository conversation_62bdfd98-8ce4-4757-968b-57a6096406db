package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuIconVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuIconVP.Param;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigSkuIconOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private ConfigSkuIconOpt.Config config;

    @Test
    public void testComputeWhenParamsInvalid() throws Throwable {
        // Given
        ConfigSkuIconOpt configSkuIconOpt = new ConfigSkuIconOpt();
        when(config.getDefaultIcon()).thenReturn("defaultIcon");
        // When
        String result = configSkuIconOpt.compute(context, param, config);
        // Then
        assertEquals("defaultIcon", result);
    }

    @Test
    public void testComputeWhenIconIsNull() throws Throwable {
        // Given
        ConfigSkuIconOpt configSkuIconOpt = new ConfigSkuIconOpt();
        when(config.getIcons()).thenReturn(new HashMap<>());
        when(config.getDefaultIcon()).thenReturn("defaultIcon");
        // When
        String result = configSkuIconOpt.compute(context, param, config);
        // Then
        assertEquals("defaultIcon", result);
    }

    @Test
    public void testComputeWhenIconIsNotNull() throws Throwable {
        // Given
        ConfigSkuIconOpt configSkuIconOpt = new ConfigSkuIconOpt();
        Map<Long, String> icons = new HashMap<>();
        icons.put(1L, "icon");
        when(config.getIcons()).thenReturn(icons);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        // Ensure the productCategory matches the key in icons
        when(skuItemDto.getProductCategory()).thenReturn(1L);
        // When
        String result = configSkuIconOpt.compute(context, param, config);
        // Then
        assertEquals("icon", result);
    }
}
