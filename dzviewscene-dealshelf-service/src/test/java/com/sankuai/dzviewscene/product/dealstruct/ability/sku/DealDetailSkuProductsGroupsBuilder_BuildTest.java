package com.sankuai.dzviewscene.product.dealstruct.ability.sku;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.NoStructDealSkuGroupBuildVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailSkuProductsGroupsBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailSkuProductsGroupsParam skuProductsGroupsParam;

    @Mock
    private DealDetailSkuProductsGroupsCfg skuProductsGroupsCfg;

    @Test
    public void testBuildWithEmptyDealDetailInfoModels() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        when(activityCxt.getSource(anyString())).thenReturn(new ArrayList<>());
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithInvalidDealDetailInfoModels() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithValidDealDetailInfoModels() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithEmptyDealSkuGroupSequenceModels() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithNonEmptyDealSkuGroupSequenceModels() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithEmptyDealSkusGroupsListAndNotEnableAdaptNoStructDeal() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        when(skuProductsGroupsCfg.isEnableAdaptNoStructDeal()).thenReturn(false);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWithNonEmptyDealSkusGroupsList() throws Throwable {
        DealDetailSkuProductsGroupsBuilder builder = new DealDetailSkuProductsGroupsBuilder();
        List<DealDetailInfoModel> dealDetailInfoModels = new ArrayList<>();
        DealDetailInfoModel detailModel = mock(DealDetailInfoModel.class);
        when(detailModel.getDealAttrs()).thenReturn(new ArrayList<>());
        dealDetailInfoModels.add(detailModel);
        when(activityCxt.getSource(anyString())).thenReturn(dealDetailInfoModels);
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);
        assertTrue(result.get().isEmpty());
    }
}
