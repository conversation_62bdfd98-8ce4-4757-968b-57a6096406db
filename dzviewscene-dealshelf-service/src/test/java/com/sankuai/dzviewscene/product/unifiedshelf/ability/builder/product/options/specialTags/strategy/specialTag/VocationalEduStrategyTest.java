package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils.SpecialTagsUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * VocationalEduStrategy.build 方法的测试类
 */
public class VocationalEduStrategyTest {

    // 免费试听课时文案key
    private static final String FREE_AUDITION_TAG_DOC = "dealEduFreeAuditionTagDocument";
    // 预约留资数文案key
    private static final String LEAD_SALES_TAG_DOC = "dealEduLeadSalesTagDocument";
    // 其他标签key
    private static final String OTHER_TAG = "attr_eduClassLocationNum";

    private VocationalEduStrategy vocationalEduStrategy;
    private SpecialTagBuildReq req;
    private ProductM productM;

    @Before
    public void setUp() {
        vocationalEduStrategy = new VocationalEduStrategy();
        req = new SpecialTagBuildReq();
        productM = mock(ProductM.class);
    }

    /**
     * 测试当 req.getProductM() 返回 null 时的情况
     */
    @Test
    public void testBuildWhenProductMIsNull() {
        // arrange
        req.setProductM(null);

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试当 ProductM 中没有任何标签时的情况
     */
    @Test
    public void testBuildWhenNoTags() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(anyString())).thenReturn("");

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当 ProductM 中只有免费试听课时的标签时的情况
     */
    @Test
    public void testBuildWithOnlyFreeAuditionTag() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(FREE_AUDITION_TAG_DOC)).thenReturn("FreeAudition");
        when(productM.getAttr(LEAD_SALES_TAG_DOC)).thenReturn("");
        when(productM.getAttr(OTHER_TAG)).thenReturn("");

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当 ProductM 中只有预约留资数量的标签时的情况
     */
    @Test
    public void testBuildWithOnlyLeadSalesTag() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(FREE_AUDITION_TAG_DOC)).thenReturn("");
        when(productM.getAttr(LEAD_SALES_TAG_DOC)).thenReturn("LeadSales");
        when(productM.getAttr(OTHER_TAG)).thenReturn("");

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当 ProductM 中同时有免费试听课时和预约留资数量的标签时的情况
     */
    @Test
    public void testBuildWithBothFreeAuditionAndLeadSalesTag() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(FREE_AUDITION_TAG_DOC)).thenReturn("FreeAudition");
        when(productM.getAttr(LEAD_SALES_TAG_DOC)).thenReturn("LeadSales");
        when(productM.getAttr(OTHER_TAG)).thenReturn("");

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertEquals(1, result.size());
        assertEquals("FreeAudition·LeadSales", result.get(0).getText().getText());
    }

    /**
     * 测试当 ProductM 中有其他标签时的情况
     */
    @Test
    public void testBuildWithOtherTag() {
        // arrange
        req.setProductM(productM);
        when(productM.getAttr(FREE_AUDITION_TAG_DOC)).thenReturn("");
        when(productM.getAttr(LEAD_SALES_TAG_DOC)).thenReturn("");
        when(productM.getAttr(OTHER_TAG)).thenReturn("OtherTag");

        // act
        List<ShelfTagVO> result = vocationalEduStrategy.build(req);

        // assert
        assertEquals(1, result.size());
        assertEquals("OtherTag", result.get(0).getText().getText());
    }
}
