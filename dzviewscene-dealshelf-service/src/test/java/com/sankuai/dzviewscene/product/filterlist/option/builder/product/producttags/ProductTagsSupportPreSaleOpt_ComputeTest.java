package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.utils.PlayActivityUtil;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductTagsSupportPreSaleOpt_ComputeTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductTagsVP.Param param;

    @Mock
    private ProductTagsSupportPreSaleOpt.Config config;

    @Mock
    private ProductM productM;

    @Mock
    private PlayActivityUtil.PlayActivityInfo playActivityInfo;

    @InjectMocks
    private ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt;

    @Test
    public void testComputeEnableBuyOneFreeOneTagIsFalse() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        List<String> result = productTagsSupportPreSaleOpt.compute(activityCxt, param, config);
        assertEquals(new ArrayList<>(), result);
    }
}
