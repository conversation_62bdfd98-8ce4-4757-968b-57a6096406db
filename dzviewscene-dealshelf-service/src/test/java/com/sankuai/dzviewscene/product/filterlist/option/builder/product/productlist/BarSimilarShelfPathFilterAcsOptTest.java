package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BarSimilarShelfPathFilterAcsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductListVP.Param param;

    @Mock
    private BarSimilarShelfPathFilterAcsOpt.Config config;

    /**
     * Tests the exception scenario.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeException() throws Throwable {
        BarSimilarShelfPathFilterAcsOpt opt = new BarSimilarShelfPathFilterAcsOpt();
        // No need to setup mocks as we are passing null to compute method to trigger NullPointerException
        opt.compute(null, null, null);
    }
}
