package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
// Corrected import for Config class
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.headpic.UnifiedDoubleColumnPicOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemPicVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedDoubleColumnPicOptComputeTest {

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private Param mockParam;

    @Mock
    private UnifiedDoubleColumnPicOpt.Config mockConfig;

    @Mock
    private ProductM mockProductM;

    @InjectMocks
    private UnifiedDoubleColumnPicOpt underTest;

    /**
     * 测试 ProductM 对象为 null 的情况
     */
    @Test
    public void testComputeProductMIsNull() throws Throwable {
        when(mockParam.getProductM()).thenReturn(null);
        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);
        assertNull(result);
    }

    /**
     * 测试 ProductM 对象不为 null，但其 picUrl 属性为空的情况
     */
    @Test
    public void testComputePicUrlIsEmpty() throws Throwable {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("");
        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);
        assertNull(result);
    }

    /**
     * 测试 ProductM 对象和 picUrl 属性都不为空的情况
     */
    @Test
    public void testComputeNormalCase() throws Throwable {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getPicUrl()).thenReturn("http://example.com/pic.jpg");
        when(mockConfig.getPicWidth()).thenReturn(640);
        when(mockConfig.getPicHeight()).thenReturn(360);
        PictureModel result = underTest.compute(mockContext, mockParam, mockConfig);
        assertNotNull(result);
        assertEquals(1.778, result.getAspectRadio(), 0.001);
    }
}
