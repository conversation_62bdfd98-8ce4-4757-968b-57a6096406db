package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.OceanCfgBean;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.bean.SceneCfgBean;
import java.util.Collections;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for UnifiedOceanConfigUtils.getTypeByConfig() method
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedOceanConfigUtilsGetTypeByConfigTest {

    /**
     * Test getTypeByConfig when oceanConfig exists with valid type
     * Should return the type value from oceanConfig
     */
    @Test
    public void testGetTypeByConfig_WithValidConfig_ShouldReturnType() {
        // arrange
        String sceneCode = "testScene";
        String expectedType = "testType";
        SceneCfgBean sceneCfgBean = new SceneCfgBean();
        sceneCfgBean.setSceneCode(sceneCode);
        sceneCfgBean.setType(expectedType);
        try (MockedStatic<UnifiedOceanConfigUtils> mockedStatic = Mockito.mockStatic(UnifiedOceanConfigUtils.class)) {
            // Mock the static method call but allow the real getTypeByConfig to execute
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getTypeByConfig(sceneCode)).thenCallRealMethod();
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getOceanConfigBySceneCode(sceneCode)).thenReturn(sceneCfgBean);
            // act
            String actualType = UnifiedOceanConfigUtils.getTypeByConfig(sceneCode);
            // assert
            assertEquals(expectedType, actualType);
        }
    }

    /**
     * Test getTypeByConfig when oceanConfig exists but type is null
     * Should return null
     */
    @Test
    public void testGetTypeByConfig_WithNullType_ShouldReturnNull() {
        // arrange
        String sceneCode = "testScene";
        SceneCfgBean sceneCfgBean = new SceneCfgBean();
        sceneCfgBean.setSceneCode(sceneCode);
        sceneCfgBean.setType(null);
        try (MockedStatic<UnifiedOceanConfigUtils> mockedStatic = Mockito.mockStatic(UnifiedOceanConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getTypeByConfig(sceneCode)).thenCallRealMethod();
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getOceanConfigBySceneCode(sceneCode)).thenReturn(sceneCfgBean);
            // act
            String actualType = UnifiedOceanConfigUtils.getTypeByConfig(sceneCode);
            // assert
            assertNull(actualType);
        }
    }

    /**
     * Test getTypeByConfig when oceanConfig exists with empty type
     * Should return empty string
     */
    @Test
    public void testGetTypeByConfig_WithEmptyType_ShouldReturnEmptyString() {
        // arrange
        String sceneCode = "testScene";
        String expectedType = "";
        SceneCfgBean sceneCfgBean = new SceneCfgBean();
        sceneCfgBean.setSceneCode(sceneCode);
        sceneCfgBean.setType(expectedType);
        try (MockedStatic<UnifiedOceanConfigUtils> mockedStatic = Mockito.mockStatic(UnifiedOceanConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getTypeByConfig(sceneCode)).thenCallRealMethod();
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getOceanConfigBySceneCode(sceneCode)).thenReturn(sceneCfgBean);
            // act
            String actualType = UnifiedOceanConfigUtils.getTypeByConfig(sceneCode);
            // assert
            assertEquals(expectedType, actualType);
        }
    }

    /**
     * Test getTypeByConfig when oceanConfig is null
     * Should return null
     */
    @Test
    public void testGetTypeByConfig_WithNullConfig_ShouldReturnNull() {
        // arrange
        String sceneCode = "testScene";
        try (MockedStatic<UnifiedOceanConfigUtils> mockedStatic = Mockito.mockStatic(UnifiedOceanConfigUtils.class)) {
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getTypeByConfig(sceneCode)).thenCallRealMethod();
            mockedStatic.when(() -> UnifiedOceanConfigUtils.getOceanConfigBySceneCode(sceneCode)).thenReturn(null);
            // act
            String actualType = UnifiedOceanConfigUtils.getTypeByConfig(sceneCode);
            // assert
            assertNull(actualType);
        }
    }
}
