package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo.CarWindiwFilmServiceInfoCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo.CarWindiwFilmServiceInfoParam;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailModuleVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class CarWindiwFilmServiceInfoBuilderTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private CarWindiwFilmServiceInfoParam carWindiwFilmServiceInfoParam;

    @Mock
    private CarWindiwFilmServiceInfoCfg cfg;

    private CarWindiwFilmServiceInfoBuilder builder;

    @Before
    public void setUp() {
        builder = new CarWindiwFilmServiceInfoBuilder();
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // arrange
        // act
        CompletableFuture<CarDetailModuleVO> result = builder.build(ctx, carWindiwFilmServiceInfoParam, cfg);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testBuildException() throws Throwable {
        // arrange
        // act
        CompletableFuture<CarDetailModuleVO> result = builder.build(ctx, carWindiwFilmServiceInfoParam, cfg);
        // assert
        // We expect a CompletableFuture, not null.
        assertNotNull(result);
        // We expect the CompletableFuture to complete with a null value.
        result.thenAccept(moduleVO -> assertNull(moduleVO));
    }
}
