package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SubProjectNumProcessorTest {

    private SubProjectNumProcessor processor = new SubProjectNumProcessor();

    @Test
    public void testProcessWhenAttrItemVOSIsNull() {
        // arrange
        ValueConfig valueKey = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        Object data = null;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testProcessWhenValuesAreEmpty() {
        // arrange
        ValueConfig valueKey = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuAttrAttrItemVO> attrItemVOS = new ArrayList<>();
        attrItemVOS.add(new SkuAttrAttrItemVO());
        Object data = attrItemVOS;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testProcessWhenNamesAreEmpty() {
        // arrange
        ValueConfig valueKey = mock(ValueConfig.class);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuAttrAttrItemVO> attrItemVOS = new ArrayList<>();
        SkuAttrAttrItemVO item = new SkuAttrAttrItemVO();
        item.setValues(Collections.singletonList(new CommonAttrVO()));
        attrItemVOS.add(item);
        Object data = attrItemVOS;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testProcessWhenFormatIsNull() {
        // arrange
        ValueConfig valueKey = mock(ValueConfig.class);
        when(valueKey.getFormat()).thenReturn(null);
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuAttrAttrItemVO> attrItemVOS = new ArrayList<>();
        SkuAttrAttrItemVO item = new SkuAttrAttrItemVO();
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName("name1");
        item.setValues(Collections.singletonList(commonAttrVO));
        attrItemVOS.add(item);
        Object data = attrItemVOS;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(1, result.size());
        assertEquals("1", result.get(0));
    }

    @Test
    public void testProcessWhenFormatIsNotNull() {
        // arrange
        ValueConfig valueKey = mock(ValueConfig.class);
        when(valueKey.getFormat()).thenReturn("format%s");
        Map<String, String> name2ValueMap = new HashMap<>();
        List<SkuAttrAttrItemVO> attrItemVOS = new ArrayList<>();
        SkuAttrAttrItemVO item = new SkuAttrAttrItemVO();
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName("name1");
        item.setValues(Collections.singletonList(commonAttrVO));
        attrItemVOS.add(item);
        Object data = attrItemVOS;
        // act
        List<String> result = processor.process(valueKey, name2ValueMap, data);
        // assert
        assertEquals(1, result.size());
        assertEquals("format1", result.get(0));
    }
}
