package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate;

import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import org.junit.Before;

public class AbstractBarSkuCreatorTest {

    private AbstractBarSkuCreator abstractBarSkuCreator;

    private BarDealDetailSkuListModuleOpt.Config config;

    /**
     * Test scenario: config.getColaSkuCateIds() or config.getDrinksSkuCateIds() are empty
     */
    @Test
    public void testIsDrinkOrColaWhenCategoryIdsAreEmpty() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = Mockito.mock(AbstractBarSkuCreator.class, Mockito.CALLS_REAL_METHODS);
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        Mockito.when(config.getColaSkuCateIds()).thenReturn(null);
        Mockito.when(config.getDrinksSkuCateIds()).thenReturn(null);
        assertFalse(abstractBarSkuCreator.isDrinkOrCola(1L, config));
    }

    /**
     * Test scenario: productCategory is not in config.getDrinksSkuCateIds() or config.getColaSkuCateIds()
     */
    @Test
    public void testIsDrinkOrColaWhenProductCategoryNotInAnyCategoryIds() throws Throwable {
        AbstractBarSkuCreator abstractBarSkuCreator = Mockito.mock(AbstractBarSkuCreator.class, Mockito.CALLS_REAL_METHODS);
        BarDealDetailSkuListModuleOpt.Config config = Mockito.mock(BarDealDetailSkuListModuleOpt.Config.class);
        Mockito.when(config.getDrinksSkuCateIds()).thenReturn(Arrays.asList(1L, 2L, 3L));
        Mockito.when(config.getColaSkuCateIds()).thenReturn(Arrays.asList(4L, 5L, 6L));
        assertFalse(abstractBarSkuCreator.isDrinkOrCola(7L, config));
    }
}
