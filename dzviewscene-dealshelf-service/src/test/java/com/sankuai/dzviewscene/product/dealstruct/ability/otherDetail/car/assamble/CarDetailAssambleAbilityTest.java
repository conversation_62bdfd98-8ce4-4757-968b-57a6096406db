package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.assamble;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailModuleVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.CompletableFuture;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.CarDetailVO;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmSpecification.CarWindomFilmSpecificationBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmserviceInfo.CarWindiwFilmServiceInfoBuilder;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CarDetailAssambleAbilityTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private CarDetailAssambleParam carDetailAssambleParam;

    @Mock
    private CarDetailAssambleCfg carDetailAssambleCfg;

    @Test
    public void testBuildWhenCarWindomFilmSpecificationIsNull() throws Throwable {
        CarDetailAssambleAbility carDetailAssambleAbility = new CarDetailAssambleAbility();
        when(ctx.getSource(CarWindomFilmSpecificationBuilder.CODE)).thenReturn(null);
        CompletableFuture<DealModuleDetailVO> result = carDetailAssambleAbility.build(ctx, carDetailAssambleParam, carDetailAssambleCfg);
        assertNotNull("Result should not be null when CarWindomFilmSpecification is null", result);
    }

    @Test
    public void testBuildWhenCarWindiwFilmServiceInfoIsNull() throws Throwable {
        CarDetailAssambleAbility carDetailAssambleAbility = new CarDetailAssambleAbility();
        when(ctx.getSource(CarWindiwFilmServiceInfoBuilder.CODE)).thenReturn(null);
        CompletableFuture<DealModuleDetailVO> result = carDetailAssambleAbility.build(ctx, carDetailAssambleParam, carDetailAssambleCfg);
        assertNotNull("Result should not be null when CarWindiwFilmServiceInfo is null", result);
    }

    @Test
    public void testBuildWhenCarWindomFilmSpecificationAndCarWindiwFilmServiceInfoAreNotNull() throws Throwable {
        CarDetailAssambleAbility carDetailAssambleAbility = new CarDetailAssambleAbility();
        CarDetailModuleVO mockModuleVO = new CarDetailModuleVO();
        when(ctx.getSource(CarWindomFilmSpecificationBuilder.CODE)).thenReturn(mockModuleVO);
        when(ctx.getSource(CarWindiwFilmServiceInfoBuilder.CODE)).thenReturn(mockModuleVO);
        CompletableFuture<DealModuleDetailVO> result = carDetailAssambleAbility.build(ctx, carDetailAssambleParam, carDetailAssambleCfg);
        assertNotNull("Result should not be null when both CarWindomFilmSpecification and CarWindiwFilmServiceInfo are not null", result);
    }
}
