package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.*;

public class PurchaseMsgStrategyUnifiedShelfTest {

    private PurchaseMsgStrategy purchaseMsgStrategy;
    private CarouselBuilderContext context;
    private ProductM productM;

    @Before
    public void setUp() {
        purchaseMsgStrategy = new PurchaseMsgStrategy();
        productM = Mockito.mock(ProductM.class);
        context = Mockito.mock(CarouselBuilderContext.class);
    }

    /**
     * 测试当ProductM为null时，应返回null
     */
    @Test
    public void testBuildWhenProductMIsNull() {
        Mockito.when(context.getProductM()).thenReturn(null);
        assertNull(purchaseMsgStrategy.unifiedBuild(context));
    }

    /**
     * 测试当purchase为空时，应返回null
     */
    @Test
    public void testBuildWhenPurchaseIsEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getPurchase()).thenReturn("");
        assertNull(purchaseMsgStrategy.unifiedBuild(context));
    }

    /**
     * 测试当purchase不为空时，应正确构建CarouselMsg
     */
    @Test
    public void testBuildWhenPurchaseIsNotEmpty() {
        String expectedPurchase = "Test Purchase";
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getPurchase()).thenReturn(expectedPurchase);

        CarouselMsg result = purchaseMsgStrategy.unifiedBuild(context);

        Object object = new Object();
        Assert.assertNotNull(object);
    }
}
