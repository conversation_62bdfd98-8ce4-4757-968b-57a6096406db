package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.specialTag;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy.SpecialTagBuildReq;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.Assert.*;

/**
 * LeastPurchaseStrategy 类的单元测试
 */
public class LeastPurchaseStrategyTest {

    private static final String PRESALE_ATTR = "preSaleTag";

    private LeastPurchaseStrategy strategy;
    private SpecialTagBuildReq req;
    private ProductM productM;

    @Before
    public void setUp() {
        strategy = new LeastPurchaseStrategy();
        req = new SpecialTagBuildReq();
        productM = Mockito.mock(ProductM.class);
    }

    /**
     * 测试 ProductM 对象为 null 的情况
     */
    @Test
    public void testBuildProductMIsNull() {
        // arrange
        req.setProductM(null);

        // act
        List<ShelfTagVO> result = strategy.build(req);

        // assert
        assertNull(result);
    }

    /**
     * 测试有最近购买信息的情况
     */
    @Test
    public void testBuildWithPurchaseInfo() {
        // arrange
        Mockito.when(productM.getPurchase()).thenReturn("2小时前有人购买");
        req.setProductM(productM);

        // act
        List<ShelfTagVO> result = strategy.build(req);

        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        // 断言可以根据实际的最近购买信息字符串进行
    }

    /**
     * 测试既不是预售产品也没有最近购买信息的情况
     */
    @Test
    public void testBuildNoPreSaleAndNoPurchaseInfo() {
        // arrange
        req.setProductM(productM);

        // act
        List<ShelfTagVO> result = strategy.build(req);

        // assert
        assertNull(result);
    }
}
