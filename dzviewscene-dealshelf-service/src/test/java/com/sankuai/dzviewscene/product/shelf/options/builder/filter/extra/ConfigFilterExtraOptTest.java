package com.sankuai.dzviewscene.product.shelf.options.builder.filter.extra;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterExtraVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConfigFilterExtraOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private FilterExtraVP.Param param;

    @Mock
    private ConfigFilterExtraOpt.Config config;

    @Mock
    private FilterBtnM filterBtnM;

    @InjectMocks
    private ConfigFilterExtraOpt configFilterExtraOpt;

    @Test
    public void testComputeExtraFromFilterIdIsNotBlank() throws Throwable {
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        Map<Long, String> filterId2FilterKey = new HashMap<>();
        filterId2FilterKey.put(1L, "filterKey");
        when(config.getFilterId2FilterKey()).thenReturn(filterId2FilterKey);
        String result = configFilterExtraOpt.compute(context, param, config);
        // Corrected assertion
        assertEquals("{\"tabKey\":\"filterKey\"}", result);
    }

    @Test
    public void testComputeDouHuListOrDouHuSkMinProductNumMapIsEmpty() throws Throwable {
        when(param.getDouHuList()).thenReturn(null);
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        Map<Long, String> filterId2FilterKey = new HashMap<>();
        when(config.getFilterId2FilterKey()).thenReturn(filterId2FilterKey);
        String result = configFilterExtraOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeProductNumGreaterThanOrEqualToMinProductNum() throws Throwable {
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk");
        Map<String, Integer> douHuSkMinProductNumMap = new HashMap<>();
        douHuSkMinProductNumMap.put("sk", 1);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Arrays.asList(new ProductM()));
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        Map<Long, String> filterId2FilterKey = new HashMap<>();
        filterId2FilterKey.put(1L, "filterKey");
        when(config.getFilterId2FilterKey()).thenReturn(filterId2FilterKey);
        String result = configFilterExtraOpt.compute(context, param, config);
        // Corrected assertion
        assertEquals("{\"tabKey\":\"filterKey\"}", result);
    }

    @Test
    public void testComputeEnableTotalProductNumIsTrue() throws Throwable {
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        Map<Long, String> filterId2FilterKey = new HashMap<>();
        filterId2FilterKey.put(1L, "filterKey");
        when(config.getFilterId2FilterKey()).thenReturn(filterId2FilterKey);
        String result = configFilterExtraOpt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeOther() throws Throwable {
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        Map<Long, String> filterId2FilterKey = new HashMap<>();
        when(config.getFilterId2FilterKey()).thenReturn(filterId2FilterKey);
        String result = configFilterExtraOpt.compute(context, param, config);
        assertNull(result);
    }
}
