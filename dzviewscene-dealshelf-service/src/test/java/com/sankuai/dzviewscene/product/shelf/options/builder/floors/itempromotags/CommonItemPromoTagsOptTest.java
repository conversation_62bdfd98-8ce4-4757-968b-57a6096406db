package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags.CommonItemPromoTagsOpt.Config;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * CommonItemPromoTagsOpt.compute方法的测试类
 */
public class CommonItemPromoTagsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private Param mockParam;
    @Mock
    private Config mockConfig;
    @Mock
    private ProductM mockProductM;

    private CommonItemPromoTagsOpt commonItemPromoTagsOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        commonItemPromoTagsOpt = new CommonItemPromoTagsOpt();
    }

    /**
     * 测试场景：当商品为次卡时
     */
    @Test
    public void testCompute_WhenProductIsTimesCard() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        when(mockProductM.getAttr("times_card_times")).thenReturn("1");

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
    }

    /**
     * 测试场景：当商品为团购次卡时
     */
    @Test
    public void testCompute_WhenProductIsDealTimesCard() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.isTimesDeal()).thenReturn(true);
        when(mockProductM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("1");

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
    }

    /**
     * 测试场景：当商品为团购次卡且配置展示单次价格
     */
    @Test
    public void testCompute_WhenProductIsDealTimesCard_matchTimeCardTagByProductTag() {
        when(mockConfig.getProductTagId2TimeCardTagMap()).thenReturn(Collections.singletonMap("1", "共%s节｜%s/节"));
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.isTimesDeal()).thenReturn(true);
        when(mockProductM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER)).thenReturn("2");
        when(mockParam.getSalePrice()).thenReturn("100");

        TagM tagM = new TagM();
        tagM.setId("1");
        Mockito.when(mockProductM.getProductTagList()).thenReturn(Lists.newArrayList(tagM));

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result.get(0).getName().equals("共2节｜50/节"));
    }

    /**
     * 测试场景：当商品既不是次卡也不是团购次卡时，且有比价标签
     */
    @Test
    public void testCompute_WhenProductIsNeitherTimesCardNorDealTimesCardAndHasPriceComparisonTag() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockProductM.getAttr("priceProtectionTag")).thenReturn("价保");

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
    }

    /**
     * 测试场景：当商品既不是次卡也不是团购次卡时，且有比价标签，指定优先级
     */
    @Test
    public void testCompute_WhenProductIsNeitherTimesCardNorDealTimesCardAndHasPriceComparisonTagWithPriority() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockConfig.getPriceTagPriority()).thenReturn(Lists.newArrayList(1));
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockProductM.getAttr("priceProtectionTag")).thenReturn("价保");

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
    }

    /**
     * 测试场景：当商品既不是次卡也不是团购次卡时，且有折扣标签
     */
    @Test
    public void testCompute_WhenProductIsNeitherTimesCardNorDealTimesCardAndHasDiscountTag() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn("100");
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockProductM.getMarketPrice()).thenReturn("200");

        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        assertNotNull(result);
    }

    /**
     * 测试场景：当商品组配置了优惠标签字符串
     */
    @Test
    public void testCompute_WhenGroupNameConfiguredWithPromoTagString() {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getGroupName()).thenReturn("堆头");
        HashMap<String, String> groupNameToPromoTagNameMap = new HashMap<>();
        groupNameToPromoTagNameMap.put("堆头", "限时优惠");
        when(mockConfig.getGroupNameToPromoTagNameMap()).thenReturn(groupNameToPromoTagNameMap);
        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("限时优惠", result.get(0).getName());
    }

    /**
     * 测试场景：当filterByProductAttr返回true时，compute方法应返回null
     */
    @Test
    public void testCompute_WhenFilterByProductAttrReturnsTrue() {
        // Given
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockParam.getSalePrice()).thenReturn("100");
        when(mockProductM.getMarketPrice()).thenReturn("200");

        // Mock filterByAttr返回值
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("color", Lists.newArrayList("red")); // 过滤条件
        when(mockConfig.getFilterByAttr()).thenReturn(filterMap);

        // Mock enableFilterByProductAttr返回值
        when(mockConfig.getExpId2EnableConfigMap()).thenReturn(new HashMap<>()); // 实验配置为空
        when(mockProductM.getAttr("color")).thenReturn("red"); // 商品属性匹配过滤条件

        // When
        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // Then
        assertEquals(0, result.size());
    }

    /**
     * 测试场景：当enableFilterByProductAttr返回false时，并且有折扣标签，compute方法应继续执行
     */
    @Test
    public void testCompute_WhenEnableFilterByProductAttrReturnsFalse() {
        // Given
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn("100");
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockProductM.getMarketPrice()).thenReturn("200");

        // Mock filterByAttr返回值
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("color", Lists.newArrayList("red")); // 过滤条件
        when(mockConfig.getFilterByAttr()).thenReturn(filterMap);

        // Mock enableFilterByProductAttr返回值
        Map<String, CommonItemPromoTagsOpt.EnableConfig> expId2EnableConfigMap = new HashMap<>();
        CommonItemPromoTagsOpt.EnableConfig enableConfig = new CommonItemPromoTagsOpt.EnableConfig();
        enableConfig.setEnableFilterByAttr(false); // 禁用过滤
        expId2EnableConfigMap.put("testExpId", enableConfig);
        when(mockConfig.getExpId2EnableConfigMap()).thenReturn(expId2EnableConfigMap);

        when(mockProductM.getAttr("color")).thenReturn("blue"); // 商品属性不匹配过滤条件

        // When
        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // Then
        assertNotNull("Result should not be null when enableFilterByProductAttr returns false", result);
    }

    /**
     * 测试场景：当enableFilterByProductAttr返回true时，compute方法应返回空列表
     */
    @Test
    public void testCompute_WhenEnableFilterByProductAttrReturnsTrue() {
        // Given
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockParam.getSalePrice()).thenReturn("100");
        when(mockProductM.getProductType()).thenReturn(0); // 非次卡类型
        when(mockProductM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(mockProductM.getMarketPrice()).thenReturn("200");

        // Mock filterByAttr返回值
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("color", Lists.newArrayList("red")); // 过滤条件
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        config.setFilterByAttr(filterMap);
        when(mockParam.getProductM().getAttr("color")).thenReturn("blue"); // 商品属性不匹配过滤条件

        // Mock enableFilterByProductAttr返回值
        Map<String, DiscountItemPromoTagsOpt.EnableConfig> expId2EnableConfigMap = new HashMap<>();
        DiscountItemPromoTagsOpt.EnableConfig enableConfig = new DiscountItemPromoTagsOpt.EnableConfig();
        enableConfig.setEnableFilterByAttr(true); // 启用过滤
        expId2EnableConfigMap.put("testExpId", enableConfig);
        config.setExpId2EnableConfigMap(expId2EnableConfigMap);

        // When
        List<DzPromoVO> result = commonItemPromoTagsOpt.compute(mockActivityCxt, mockParam, mockConfig);

        // Then
        assertNotNull("Result should not be null when enableFilterByProductAttr returns false", result);
    }

}
