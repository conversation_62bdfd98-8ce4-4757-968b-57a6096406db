package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicActivityTagVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class PromoCodePicActivityTagOptTest {

    private PromoCodePicActivityTagOpt opt;
    private PromoCodePicActivityTagOpt.Config config;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new PromoCodePicActivityTagOpt();
        config = new PromoCodePicActivityTagOpt.Config();
    }

    @Test
    public void testComputeWithNullProduct() {
        ProductPicActivityTagVP.Param param = ProductPicActivityTagVP.Param.builder()
                .productM(null)
                .build();

        List<DzActivityTagVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithEmptyExtAttrs() {
        ProductPicActivityTagVP.Param param = ProductPicActivityTagVP.Param.builder()
                .productM(productM)
                .build();

        when(productM.getExtAttrs()).thenReturn(null);

        List<DzActivityTagVO> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithMatchingAttr() {
        // 使用建造器创建参数对象
        ProductPicActivityTagVP.Param param = ProductPicActivityTagVP.Param.builder()
                .productM(productM)
                .build();

        // 配置TagCfg
        PromoCodePicActivityTagOpt.TagCfg tagCfg = new PromoCodePicActivityTagOpt.TagCfg();
        tagCfg.setPicUrl("http://test.com/pic.jpg");
        tagCfg.setLabel("测试标签");
        tagCfg.setBackgroundImg("http://test.com/bg.jpg");
        tagCfg.setPosition(1);
        tagCfg.setTargetNums(2);

        // 配置属性映射
        Map<String, PromoCodePicActivityTagOpt.TagCfg> attrMap = Maps.newHashMap();
        attrMap.put("testAttr", tagCfg);
        config.setProductExtAttrs2TagCfg(attrMap);

        // 配置商品属性
        AttrM attr = new AttrM();
        attr.setName("testAttr");
        attr.setValue("3");
        when(productM.getExtAttrs()).thenReturn(Lists.newArrayList(attr));

        List<DzActivityTagVO> result = opt.compute(context, param, config);

        assertNotNull(result);
        assertEquals(1, result.size());
        DzActivityTagVO tagVO = result.get(0);
        assertEquals("http://test.com/pic.jpg", tagVO.getImgUrl());
        assertEquals("测试标签", tagVO.getLabel());
        assertEquals("http://test.com/bg.jpg", tagVO.getBackgroundImg());
        assertEquals(1, tagVO.getPosition());
    }

    @Test
    public void testComputeWithNonMatchingAttr() {
        ProductPicActivityTagVP.Param param = ProductPicActivityTagVP.Param.builder()
                .productM(productM)
                .build();

        // 配置TagCfg
        PromoCodePicActivityTagOpt.TagCfg tagCfg = new PromoCodePicActivityTagOpt.TagCfg();
        tagCfg.setTargetNums(5);

        // 配置属性映射
        Map<String, PromoCodePicActivityTagOpt.TagCfg> attrMap = Maps.newHashMap();
        attrMap.put("testAttr", tagCfg);
        config.setProductExtAttrs2TagCfg(attrMap);

        // 配置商品属性
        AttrM attr = new AttrM();
        attr.setName("testAttr");
        attr.setValue("3");
        when(productM.getExtAttrs()).thenReturn(Lists.newArrayList(attr));

        List<DzActivityTagVO> result = opt.compute(context, param, config);

        assertNull(result);
    }
}