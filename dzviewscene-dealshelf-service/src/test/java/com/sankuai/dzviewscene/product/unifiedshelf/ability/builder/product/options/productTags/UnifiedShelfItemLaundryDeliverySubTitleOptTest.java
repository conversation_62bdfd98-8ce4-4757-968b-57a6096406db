package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试 UnifiedShelfItemLaundryDeliverySubTitleOpt 的 compute 方法
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemLaundryDeliverySubTitleOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private UnifiedShelfItemLaundryDeliverySubTitleOpt.Config mockConfig;
    @Mock
    private ProductM mockProductM;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param param;
    @InjectMocks
    private UnifiedShelfItemLaundryDeliverySubTitleOpt optUnderTest;

    /**
     * 测试产品支持免费取送服务的场景
     */
    @Test
    public void testComputeSupportsLaundryDelivery() {
        when(param.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getAttr("lifeServiceLaundrySupportDelivery")).thenReturn("免费取送");

        ItemSubTitleVO result = optUnderTest.compute(mockActivityCxt, param, mockConfig);

        assertEquals("免费取送", result.getTags().get(0).getText());
    }

    /**
     * 测试产品不支持免费取送服务的场景
     */
    @Test
    public void testComputeDoesNotSupportLaundryDelivery() {
        when(param.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getAttr("lifeServiceLaundrySupportDelivery")).thenReturn("其他服务");

        ItemSubTitleVO result = optUnderTest.compute(mockActivityCxt, param, mockConfig);

        assertNotNull(result);
        assertNotEquals("免费取送", result.getTags().get(0).getText());
    }

    /**
     * 测试产品属性为空的场景
     */
    @Test
    public void testComputeWithEmptyAttribute() {
        when(param.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getAttr("lifeServiceLaundrySupportDelivery")).thenReturn("");

        ItemSubTitleVO result = optUnderTest.compute(mockActivityCxt, param, mockConfig);

        assertTrue(result.getTags().isEmpty());
    }

    /**
     * 测试 ProductM 对象为 null 的场景
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWithNullProductM() {
        ItemSubTitleVO result = optUnderTest.compute(mockActivityCxt, param, mockConfig);
    }
}
