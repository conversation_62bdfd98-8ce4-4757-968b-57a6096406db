package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import java.util.LinkedHashMap;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class FloatTagBuildStrategyFactoryGetFirstValidTagTest {

    @InjectMocks
    private FloatTagBuildStrategyFactory factory;

    @Mock
    private FloatTagBuildStrategy mockStrategy;

    @Mock
    private ApplicationContext applicationContext;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(factory, "appCtx", applicationContext);
        when(applicationContext.getBeansOfType(FloatTagBuildStrategy.class)).thenReturn(java.util.Collections.singletonMap("testStrategy", mockStrategy));
        when(mockStrategy.getName()).thenReturn("testStrategyName");
    }

    @Test
    public void testGetFirstValidTag_CfgMapNull() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagVO result = factory.getFirstValidTag(param, null);
        assertNull(result);
    }

    @Test
    public void testGetFirstValidTag_CfgMapEmpty() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        LinkedHashMap<String, FloatTagBuildCfg> cfgMap = new LinkedHashMap<>();
        FloatTagVO result = factory.getFirstValidTag(param, cfgMap);
        assertNull(result);
    }

    @Test
    public void testGetFirstValidTag_StrategyNull() throws Throwable {
        FloatTagBuildReq param = new FloatTagBuildReq();
        LinkedHashMap<String, FloatTagBuildCfg> cfgMap = new LinkedHashMap<>();
        // Simulate a scenario where the strategy is null
        cfgMap.put("invalidStrategy", new FloatTagBuildCfg());
        FloatTagVO result = factory.getFirstValidTag(param, cfgMap);
        assertNull(result);
    }
}
