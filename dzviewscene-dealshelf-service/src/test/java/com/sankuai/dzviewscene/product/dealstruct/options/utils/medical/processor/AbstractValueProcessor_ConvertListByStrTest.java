package com.sankuai.dzviewscene.product.dealstruct.options.utils.medical.processor;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.product.dealstruct.vo.medical.ValueConfig;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractValueProcessor_ConvertListByStrTest {

    private AbstractValueProcessor processor = new AbstractValueProcessor() {

        @Override
        public List<String> convertDisplayValues(String value, ValueConfig valueConfig, Map<String, String> name2ValueMap) {
            return null;
        }

        @Override
        public List<String> process(ValueConfig valueConfig, Map<String, String> name2ValueMap, Object data) {
            return null;
        }
    };

    private MockedStatic<JsonCodec> mockedJsonCodec;

    @Before
    public void setUp() {
        mockedJsonCodec = mockStatic(JsonCodec.class);
    }

    @After
    public void tearDown() {
        mockedJsonCodec.close();
    }

    @Test
    public void testConvertListByStr_ValidJson() throws Throwable {
        String value = "[\"test\"]";
        List<String> expected = Arrays.asList("test");
        mockedJsonCodec.when(() -> JsonCodec.decode(eq(value), any(TypeReference.class))).thenReturn(expected);
        List<String> result = processor.convertListByStr(value);
        assertEquals(expected, result);
    }

    @Test
    public void testConvertListByStr_InvalidJson() throws Throwable {
        String value = "invalid json";
        mockedJsonCodec.when(() -> JsonCodec.decode(eq(value), any(TypeReference.class))).thenReturn(null);
        List<String> result = processor.convertListByStr(value);
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertListByStr_EmptyString() throws Throwable {
        String value = "";
        mockedJsonCodec.when(() -> JsonCodec.decode(eq(value), any(TypeReference.class))).thenReturn(null);
        List<String> result = processor.convertListByStr(value);
        assertEquals(0, result.size());
    }

    @Test
    public void testConvertListByStr_Null() throws Throwable {
        String value = null;
        mockedJsonCodec.when(() -> JsonCodec.decode(eq(value), any(TypeReference.class))).thenReturn(null);
        List<String> result = processor.convertListByStr(value);
        assertEquals(0, result.size());
    }
}
