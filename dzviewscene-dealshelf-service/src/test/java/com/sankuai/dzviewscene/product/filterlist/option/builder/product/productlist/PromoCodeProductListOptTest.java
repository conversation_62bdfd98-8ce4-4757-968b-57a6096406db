package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.filterlist.utils.PromoCodeUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DisableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.common.model.UseRuleM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

public class PromoCodeProductListOptTest {

    private PromoCodeProductListOpt opt;
    private PromoCodeProductListOpt.Config config;

    @Mock
    private ActivityCxt context;
    private MockedStatic<PromoCodeUtils> promoCodeUtilsMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        opt = new PromoCodeProductListOpt();
        config = new PromoCodeProductListOpt.Config();
        promoCodeUtilsMockedStatic = Mockito.mockStatic(PromoCodeUtils.class);
    }

    // 还原配置
    @After
    public void afterTest() {
        config.setUseChangePriceFilter(false);
        config.setUseFilterValid(false);
        config.setUseTop(false);
        promoCodeUtilsMockedStatic.close();
    }

    @Test
    public void testComputeWithEmptyProducts() {
        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(null)
                .build();

        List<ProductM> result = opt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeWithValidFilter() {
        // 准备测试数据
        ProductM validProduct = createValidProduct();
        ProductM invalidProduct = createInvalidProduct();

        List<ProductM> products = Lists.newArrayList(validProduct, invalidProduct);

        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(products)
                .build();

        // 启用过滤
        config.setUseFilterValid(true);
        config.setUseTop(false);

        List<ProductM> result = opt.compute(context, param, config);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(validProduct, result.get(0));
    }

    @Test
    public void testComputeWithTopSort() {
        // 准备测试数据
        ProductM purchasedProduct = createValidProduct();
        purchasedProduct.setAttr("dealUserPurchaseCountAttr", "1");

        ProductM unpurchasedProduct = createValidProduct();
        unpurchasedProduct.setAttr("dealUserPurchaseCountAttr", "0");

        List<ProductM> products = Lists.newArrayList(unpurchasedProduct, purchasedProduct);

        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(products)
                .build();

        // 启用置顶
        config.setUseFilterValid(false);
        config.setUseTop(true);

        List<ProductM> result = opt.compute(context, param, config);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(purchasedProduct, result.get(0)); // 购买过的商品应该在前
        assertEquals(unpurchasedProduct, result.get(1));
    }

    @Test
    public void testComputeWithBothFilterAndSort() {
        // 准备测试数据
        ProductM validPurchased = createValidProduct();
        validPurchased.setAttr("dealUserPurchaseCountAttr", "1");

        ProductM validUnpurchased = createValidProduct();
        validUnpurchased.setAttr("dealUserPurchaseCountAttr", "0");

        ProductM invalidPurchased = createInvalidProduct();
        invalidPurchased.setAttr("dealUserPurchaseCountAttr", "1");

        List<ProductM> products = Lists.newArrayList(invalidPurchased, validUnpurchased, validPurchased);

        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(products)
                .build();

        // 同时启用过滤和置顶
        config.setUseFilterValid(true);
        config.setUseTop(true);

        List<ProductM> result = opt.compute(context, param, config);

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(validPurchased, result.get(0));
        assertEquals(validUnpurchased, result.get(1));
    }

    private ProductM createValidProduct() {
        ProductM product = new ProductM();
        product.setSalePrice(BigDecimal.valueOf(100L));
        product.setAttr("productType", "deal");
        product.setAttr("dealStatusAttr", "true");
        product.setAttr("attr_search_hidden_status", "false");
        product.setSale(new ProductSaleM());
        return product;
    }

    private ProductM createInvalidProduct() {
        ProductM product = new ProductM();
        product.setSalePrice(BigDecimal.valueOf(100L));
        product.setAttr("productType", "notDeal");
        product.setAttr("dealStatusAttr", "false");
        product.setAttr("attr_search_hidden_status", "true");
        return product;
    }

    private ProductM createPreSaleTagProduct() {
        ProductM product = new ProductM();
        product.setSalePrice(BigDecimal.valueOf(100L));
        product.setAttr("attr_is_pre_sale", "true");
        return product;
    }

    private ProductM createNoUseRuleProduct() {
        ProductM product = new ProductM();
        product.setSalePrice(BigDecimal.valueOf(100L));
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDateM = new DisableDateM();
        disableDateM.setDisableDays(Lists.newArrayList(1, 2, 3));
        useRuleM.setDisableDate(disableDateM);
        product.setUseRuleM(useRuleM);
        return product;
    }

    /**
     * 测试 compute 方法在启用价格过滤且门店自动验证通过时的行为
     */
    @Test
    public void testComputeWithChangePriceFilterAndShopAutoVerify() {
        // arrange
        config.setUseChangePriceFilter(true);
        ProductM preSaleTagProduct = createPreSaleTagProduct();
        ProductM noUseRuleProduct = createNoUseRuleProduct();
        ProductM validProduct = createValidProduct();

        List<ProductM> products = new ArrayList<>();
        products.add(preSaleTagProduct);
        products.add(noUseRuleProduct);
        products.add(validProduct);
        promoCodeUtilsMockedStatic.when(() -> PromoCodeUtils.checkShopAutoVerify(context)).thenReturn(true);
        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(products)
                .build();

        // act
        List<ProductM> result = opt.compute(context, param, config);

        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains(validProduct));
    }

    /**
     * 测试 compute 方法在启用价格过滤但门店自动验证未通过时的行为
     */
    @Test
    public void testComputeWithChangePriceFilterAndShopAutoVerifyFail() {
        // arrange
        config.setUseChangePriceFilter(true);
        config.setUseChangePriceFilter(true);
        ProductM preSaleTagProduct = createPreSaleTagProduct();
        ProductM noUseRuleProduct = createNoUseRuleProduct();
        ProductM validProduct = createValidProduct();

        List<ProductM> products = new ArrayList<>();
        products.add(preSaleTagProduct);
        products.add(noUseRuleProduct);
        products.add(validProduct);
        promoCodeUtilsMockedStatic.when(() -> PromoCodeUtils.checkShopAutoVerify(context)).thenReturn(false);
        ProductListVP.Param param = ProductListVP.Param.builder()
                .productMS(products)
                .build();

        // act

        // act
        List<ProductM> result = opt.compute(context, param, config);

        // assert
        assertEquals(products, result);
    }
}