package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.core.config.Config;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoDetailVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CardItemPriceBottomTagsOptBuildPreSalePromoTagsTest {

    @InjectMocks
    private CardItemPriceBottomTagsOpt cardItemPriceBottomTagsOpt;

    @Mock
    private ProductM productM;

    @Mock
    private ItemPriceBottomTagsVP.Param param;

    @Mock
    private CardItemPriceBottomTagsOpt.Config config;

    @Mock
    private ActivityCxt context;

    @Mock
    private List<ProductPromoPriceM> promoPrices;

    /**
     * Test case for user holding card promo without dao gua
     */
    @Test
    public void testBuildPreSalePromoTags_UserHoldCardPromoWithoutDaoGua() throws Throwable {
        // Setup
        ItemPriceBottomTagsVP.Param param = mock(ItemPriceBottomTagsVP.Param.class);
        when(param.getProductM()).thenReturn(productM);
        when(param.getPlatform()).thenReturn(1);
        CardM cardM = new CardM();
        cardM.setUserCardList(Lists.newArrayList(1));
        when(param.getCardM()).thenReturn(cardM);
        CardItemPriceBottomTagsOpt.Config config = new CardItemPriceBottomTagsOpt.Config();
        config.setPreSaleBorderColor("#BORDER");
        config.setPreSaleBackground("#BG");
        config.setPreSaleTextColor("#TEXT");
        config.setPopType(3);
        config.setPrePic(new DzPictureComponentVO());
        config.setAfterPic(new DzPictureComponentVO());
        List<ProductPromoPriceM> promoPrices = new ArrayList<>();
        ProductPromoPriceM cardPromo = new ProductPromoPriceM();
        cardPromo.setPromoType(1);
        cardPromo.setPromoPrice(new BigDecimal("80"));
        cardPromo.setPromoTag("Card Promo");
        // Set totalPromoPrice
        cardPromo.setTotalPromoPrice(new BigDecimal("80"));
        promoPrices.add(cardPromo);
        when(productM.getPromoPrices()).thenReturn(promoPrices);
        when(productM.getPromo(anyInt())).thenReturn(null);
        // Act
        DzTagVO result = cardItemPriceBottomTagsOpt.buildPreSalePromoTags(param, config);
        // Assert
        assertNotNull(result);
        assertTrue(result.getHasBorder());
        assertEquals(config.getPreSaleBorderColor(), result.getBorderColor());
        assertEquals(config.getPreSaleBackground(), result.getBackground());
        assertEquals(config.getPreSaleTextColor(), result.getTextColor());
        assertEquals(config.getPrePic(), result.getPrePic());
        assertEquals(config.getAfterPic(), result.getAfterPic());
    }

    /**
     * Test case for null tag string scenario
     */
    @Test
    public void testBuildPreSalePromoTags_NullTagString() throws Throwable {
        // Setup
        ItemPriceBottomTagsVP.Param param = mock(ItemPriceBottomTagsVP.Param.class);
        when(param.getProductM()).thenReturn(productM);
        CardItemPriceBottomTagsOpt.Config config = new CardItemPriceBottomTagsOpt.Config();
        List<ProductPromoPriceM> promoPrices = new ArrayList<>();
        ProductPromoPriceM promo = new ProductPromoPriceM();
        promo.setPromoType(0);
        promoPrices.add(promo);
        when(productM.getPromoPrices()).thenReturn(promoPrices);
        when(productM.getPromo(anyInt())).thenReturn(promo);
        // Act
        DzTagVO result = cardItemPriceBottomTagsOpt.buildPreSalePromoTags(param, config);
        // Assert
        assertNull(result);
    }

    /**
     * 测试场景：预售标签不展示，且“全网低价”标签为空
     * 预期结果：返回包含卡片价格促销标签的列表
     */
    @Test
    public void testComputeNoPreSaleTagAndEmptyLowestPriceTag() throws Throwable {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        // Initialize promoPrices
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        when(cardItemPriceBottomTagsOpt.buildPreSalePromoTags(param, config)).thenReturn(null);
        // Since we cannot mock the private method, we directly set up the conditions
        // act
        List<DzTagVO> result = cardItemPriceBottomTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：预售标签不展示，且卡片价格促销标签和“全网低价”标签都为空
     * 预期结果：返回空列表
     */
    @Test
    public void testComputeNoPreSaleTagAndBothTagsEmpty() throws Throwable {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        // Initialize promoPrices
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        when(cardItemPriceBottomTagsOpt.buildPreSalePromoTags(param, config)).thenReturn(null);
        // Since we cannot mock the private method, we directly set up the conditions
        // act
        List<DzTagVO> result = cardItemPriceBottomTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：预售标签展示，但预售标签生成失败（返回null）
     * 预期结果：返回空列表
     */
    @Test
    public void testComputePreSaleTagShownButNull() throws Throwable {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        // Initialize promoPrices
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        when(cardItemPriceBottomTagsOpt.buildPreSalePromoTags(param, config)).thenReturn(null);
        // Since we cannot mock the private method, we directly set up the conditions
        // act
        List<DzTagVO> result = cardItemPriceBottomTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试场景：参数为null
     * 预期结果：抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWithNullParam() throws Throwable {
        // act
        cardItemPriceBottomTagsOpt.compute(context, null, config);
    }

    /**
     * 测试场景：配置为null
     * 预期结果：抛出NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testComputeWithNullConfig() throws Throwable {
        // act
        cardItemPriceBottomTagsOpt.compute(context, param, null);
    }
}
