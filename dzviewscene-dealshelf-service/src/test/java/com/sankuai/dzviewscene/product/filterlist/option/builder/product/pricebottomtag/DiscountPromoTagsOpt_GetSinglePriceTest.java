package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

/**
 * Tests for DiscountPromoTagsOpt.getSinglePrice method
 */
public class DiscountPromoTagsOpt_GetSinglePriceTest {

    private ProductM productM;

    /**
     * Test getSinglePrice with null salePrice
     */
    @Test
    public void testGetSinglePriceWithNullSalePrice() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String result = opt.getSinglePrice(productM, null);
        Assert.assertNull("The result should be null when salePrice is null", result);
    }

    /**
     * Test getSinglePrice with blank salePrice
     */
    @Test
    public void testGetSinglePriceWithBlankSalePrice() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String result = opt.getSinglePrice(productM, " ");
        Assert.assertNull("The result should be null when salePrice is blank", result);
    }

    /**
     * Test getSinglePrice with non-digit TIMES_DEAL_SALE_NUMBER attribute
     */
    @Test
    public void testGetSinglePriceWithNonDigitAttr() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("non-digit");
        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String result = opt.getSinglePrice(productM, "100");
        Assert.assertNull("The result should be null when TIMES_DEAL_SALE_NUMBER attribute is non-digit", result);
    }

    /**
     * Test getSinglePrice with valid inputs
     */
    @Test
    public void testGetSinglePriceWithValidInputs() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("2");
        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String result = opt.getSinglePrice(productM, "100");
        Assert.assertNotNull("The result should not be null with valid inputs", result);
        Assert.assertEquals("The expected single price is incorrect", "50", result);
    }

    /**
     * Test getSinglePrice with arithmetic exception
     */
    @Test
    public void testGetSinglePriceWithArithmeticException() throws Throwable {
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getAttr("sys_multi_sale_number")).thenReturn("0");
        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String result = opt.getSinglePrice(productM, "100");
        Assert.assertNull("The result should be null when an arithmetic exception occurs", result);
    }
}
