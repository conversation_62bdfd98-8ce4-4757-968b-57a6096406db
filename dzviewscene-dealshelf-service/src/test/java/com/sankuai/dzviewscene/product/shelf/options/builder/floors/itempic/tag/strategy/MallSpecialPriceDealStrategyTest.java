package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.dianping.vc.sdk.dp.config.LionObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Constructor;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class MallSpecialPriceDealStrategyTest {

    private static MockedStatic<LionObject> lionObjectMockedStatic;

    @BeforeClass
    public static void beforeClass()throws Exception {
        lionObjectMockedStatic = Mockito.mockStatic(LionObject.class);
        Constructor constructor = LionObject.class.getDeclaredConstructor(Object.class, LionObject.ObjectBuilder.class);
        constructor.setAccessible(true);
        MallSpecialPriceDealStrategy.Config config = new MallSpecialPriceDealStrategy.Config();
        config.setPicUrl("https://p0.meituan.net/ingee/c83a580ad50354eb107ee165a338de003386.png");
        config.setPicHeight(66);
        config.setAspectRadio(1d);
        LionObject<MallSpecialPriceDealStrategy.Config> configLionObject = (LionObject<MallSpecialPriceDealStrategy.Config>) constructor.newInstance(config, null);
        lionObjectMockedStatic.when(()->LionObject.create(Mockito.anyString(), (TypeReference<?>) Mockito.any())).thenReturn(configLionObject);
    }

    @AfterClass
    public static void afterClass(){
        lionObjectMockedStatic.close();
    }

    /**
     * Test the buildTag method under normal conditions.
     */
    @Test
    public void testBuildTagNormal() throws Throwable {
        // Arrange
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        // Act
        FloatTagVO result = strategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals("https://p0.meituan.net/ingee/c83a580ad50354eb107ee165a338de003386.png", result.getIcon().getPicUrl());
        assertEquals(66, result.getIcon().getPicHeight());
        assertEquals(1, result.getIcon().getAspectRadio(), 0.01);
    }

    /**
     * Test the buildTag method when param is null.
     */
    @Test
    public void testBuildTagParamNull() throws Throwable {
        // Arrange
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        // Act
        FloatTagVO result = strategy.buildTag(null, config);
        // Assert
        assertNotNull("Expected non-null result when param is null", result);
    }

    /**
     * Test the buildTag method when config is null.
     */
    @Test
    public void testBuildTagConfigNull() throws Throwable {
        // Arrange
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        // Act
        FloatTagVO result = strategy.buildTag(param, null);
        // Assert
        assertNotNull("Expected non-null result when config is null", result);
    }

    @Test
    public void testBuildTag() {
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        FloatTagVO result = strategy.buildTag(param, config);
        Assert.assertTrue(Objects.nonNull(result) && Objects.nonNull(result.getIcon()));
    }

    @Test
    public void testBuildTagWithConfigHeightNonNull() {
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setHeight(86);
        FloatTagVO result = strategy.buildTag(param, config);
        Assert.assertTrue(Objects.nonNull(result) && Objects.nonNull(result.getIcon()));
    }

    @Test
    public void testBuildTagWithConfigUrlNonNull() {
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicUrl("test");
        FloatTagVO result = strategy.buildTag(param, config);
        Assert.assertTrue(Objects.nonNull(result) && Objects.nonNull(result.getIcon()));
    }

    @Test
    public void testBuildTagWithConfigAspectRadioNonNull() {
        MallSpecialPriceDealStrategy strategy = new MallSpecialPriceDealStrategy();
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicAspectRadio(2.5);
        FloatTagVO result = strategy.buildTag(param, config);
        Assert.assertTrue(Objects.nonNull(result) && Objects.nonNull(result.getIcon()));
    }
}
