package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.PictureModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.title.UnifiedShelfMultiActivityTitleOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterTitleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试UnifiedShelfMultiActivityTitleOpt类的buildPictureModel方法
 */
public class UnifiedShelfMultiActivityTitleOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private UnifiedShelfMultiActivityTitleOpt.Config config;
    @Mock
    private FilterBtnM filterBtnM;
    @Mock
    private ProductActivityM productActivityM;

    private UnifiedShelfMultiActivityTitleOpt target;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        target = new UnifiedShelfMultiActivityTitleOpt();
    }

    /**
     * 测试当神券配置匹配时，应返回正确的图片模型
     */
    @Test
    public void testBuildPictureModel_WithMagicalMemberMatch() {
        // arrange
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(1L);
        UnifiedShelfMultiActivityTitleOpt.ActivityBtnConfig magicalMemberConfig = new UnifiedShelfMultiActivityTitleOpt.ActivityBtnConfig();
        magicalMemberConfig.setFilterIds(Arrays.asList(1L));
        when(config.getMagicalMemberConfig()).thenReturn(magicalMemberConfig);

        // act
        PictureModel result = target.buildPictureModel(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("https://p0.meituan.net/ingee/89373b5c501690ce41188a0e9ff1e0cb4808.png", result.getPicUrl());
    }

    /**
     * 测试当没有匹配的配置时，应返回null
     */
    @Test
    public void testBuildPictureModel_WithNoMatch() {
        // arrange
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        when(filterBtnM.getFilterId()).thenReturn(4L);

        // act
        PictureModel result = target.buildPictureModel(activityCxt, param, config);

        // assert
        assertNull(result);
    }
}

