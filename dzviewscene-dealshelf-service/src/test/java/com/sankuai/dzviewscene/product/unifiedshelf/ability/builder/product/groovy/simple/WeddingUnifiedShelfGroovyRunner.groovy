package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.simple

import com.google.common.collect.Lists
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyDouHuSkExc
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.groovy.UnifiedShelfGroovyRunner

/**
 * 婚纱摄影的执行器
 */
class WeddingUnifiedShelfGroovyRunner extends UnifiedShelfGroovyRunner<ItemSubTitleVO> {

    @Override
    protected List<UnifiedShelfGroovyDouHuSkExc<ItemSubTitleVO>> getDouHuExcList() {
        return Lists.newArrayList(new WeddingUnifiedShelfGroovyDouHuAExc<ItemSubTitleVO>(),
                new WeddingUnifiedShelfGroovyDouHuBExc<ItemSubTitleVO>())
    }
}
