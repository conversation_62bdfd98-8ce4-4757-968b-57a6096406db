package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopSuperVogueStrategyBuildTagTest {

    @InjectMocks
    private ShopSuperVogueStrategy shopSuperVogueStrategy;

    @Mock
    private FloatTagBuildReq param;

    /**
     * Tests the buildTag method under normal conditions.
     */
    @Test
    public void testBuildTagNormal() throws Throwable {
        // Arrange
        when(param.getShelfShowType()).thenReturn(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        // Act
        FloatTagVO result = shopSuperVogueStrategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals(16, result.getIcon().getPicHeight());
    }

    /**
     * Tests the buildTag method when param is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildTagParamNull() throws Throwable {
        // Act
        shopSuperVogueStrategy.buildTag(null, new FloatTagBuildCfg());
    }

    /**
     * Tests the buildTag method when shelfShowType does not exist in the mapping.
     */
    @Test
    public void testBuildTagShelfShowTypeNotExists() throws Throwable {
        // Arrange
        when(param.getShelfShowType()).thenReturn(999);
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        // Act
        FloatTagVO result = shopSuperVogueStrategy.buildTag(param, config);
        // Assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals(0, result.getIcon().getPicHeight());
    }
}
