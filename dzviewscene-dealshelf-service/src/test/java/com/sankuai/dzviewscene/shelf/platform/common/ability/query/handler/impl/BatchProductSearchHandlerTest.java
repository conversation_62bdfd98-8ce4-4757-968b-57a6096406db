package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.general.unified.search.api.generalsearchv2.dto.ProductSearchIdDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.ProductSearchRequestV2;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class BatchProductSearchHandlerTest {

    @InjectMocks
    private BatchProductSearchHandler batchProductSearchHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    private ActivityContext activityContext;

    private String groupName;

    private Map<String, Object> params;

    @Before
    public void setUp() {
        activityContext = new ActivityContext();
        groupName = "testGroup";
        params = new HashMap<>();
    }

    /**
     * 测试 query 方法在正常情况下的行为
     */
    @Test
    public void testQueryNormal() throws Throwable {
        // arrange
        ProductSearchResponse<ProductSearchIdDto> productSearchResponse = new ProductSearchResponse<>();
        when(compositeAtomService.searchProductServiceV2(any(ProductSearchRequestV2.class))).thenReturn(CompletableFuture.completedFuture(productSearchResponse));
        // act
        CompletableFuture<ProductGroupM> result = batchProductSearchHandler.query(activityContext, groupName, params);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNotNull(result.get());
    }

    /**
     * 测试 query 方法在异常情况下的行为
     */
    @Test(expected = RuntimeException.class)
    public void testQueryException() throws Throwable {
        // arrange
        when(compositeAtomService.searchProductServiceV2(any(ProductSearchRequestV2.class))).thenThrow(new RuntimeException());
        // act
        batchProductSearchHandler.query(activityContext, groupName, params);
        // assert
        // Exception is expected
    }
}
