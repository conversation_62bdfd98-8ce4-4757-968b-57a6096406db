package com.sankuai.dzviewscene.shelf.platform.utils;

import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/10 12:03 下午
 */
public class CollectUtilsUnitTest {

    //@Test
    @DisplayName("测试 获取一个List集合中的第一个值 函数firstValue 入参list为null的情况")
    public void test_firstValue_given_nullList_should_return_null() {
        List list = null;
        Assert.assertTrue(CollectUtils.firstValue(list) == null);
    }

    //@Test
    @DisplayName("测试 获取一个List集合中的第一个值 函数firstValue 入参list为空list的情况")
    public void test_firstValue_given_emptyList_should_return_null() {
        List list = Lists.newArrayList();
        Assert.assertTrue(CollectUtils.firstValue(list) == null);
    }

    //@Test
    @DisplayName("测试 获取一个List集合中的第一个值 函数firstValue 入参list不为空的情况")
    public void test_firstValue_given_list_should_return_firstVlue() {
        Object object = new Object();
        List list = Lists.newArrayList(object);
        Assert.assertTrue(CollectUtils.firstValue(list) == object);
    }

}
