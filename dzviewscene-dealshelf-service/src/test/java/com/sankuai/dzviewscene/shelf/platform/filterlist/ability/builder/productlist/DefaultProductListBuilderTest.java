package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.framework.exception.ComponentNotFoundException;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.powermock.core.classloader.annotations.PrepareForTest;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductListBuilderTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private CompletableFuture<Object> productItemsCompletableFuture;

    private DefaultProductListBuilder builder;

    @InjectMocks
    private DefaultProductListBuilder defaultProductListBuilder;

    @Mock
    private ComponentFinder componentFinder;

    @Mock
    private ProductListBuilderExt productListBuilderExt;

    @Before
    public void setUp() {
        builder = new DefaultProductListBuilder();
    }

    @Test
    public void testShowTypeWhenActivityContextIsNull() throws Throwable {
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(null);
        assertEquals(0, result);
    }

    @Test
    public void testShowTypeWhenShowTypeIsNotInteger() throws Throwable {
        when(activityContext.getParameters()).thenReturn(new HashMap<>());
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(activityContext);
        assertEquals(0, result);
    }

    @Test
    public void testShowTypeWhenShowTypeIsGreaterThanZero() throws Throwable {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 1);
        when(activityContext.getParameters()).thenReturn(parameters);
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(activityContext);
        assertEquals(1, result);
    }

    @Test
    public void testShowTypeWhenShowTypeIsNotGreaterThanZeroAndExtPointIsFound() throws Throwable {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(activityContext);
        assertEquals(0, result);
    }

    @Test
    public void testShowTypeWhenComponentNotFoundExceptionOccurs() throws Throwable {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(activityContext);
        assertEquals(0, result);
    }

    @Test
    public void testShowTypeWhenOtherExceptionOccurs() throws Throwable {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        DefaultProductListBuilder builder = new DefaultProductListBuilder();
        int result = builder.showType(activityContext);
        assertEquals(0, result);
    }

    /**
     * 测试build方法，正常情况
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // arrange
        when(activityContext.getAttachment(anyString())).thenReturn(productItemsCompletableFuture, productItemsCompletableFuture);
        // act
        CompletableFuture<DzFilterProductListVO> result = builder.build(activityContext);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试build方法，异常情况
     */
    @Test
    public void testBuildException() throws Throwable {
        // arrange
        when(activityContext.getAttachment(anyString())).thenReturn(productItemsCompletableFuture, productItemsCompletableFuture);
        // act
        CompletableFuture<DzFilterProductListVO> result = builder.build(activityContext);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试build方法，边界情况
     */
    @Test
    public void testBuildBoundary() throws Throwable {
        // arrange
        when(activityContext.getAttachment(anyString())).thenReturn(productItemsCompletableFuture, productItemsCompletableFuture);
        // act
        CompletableFuture<DzFilterProductListVO> result = builder.build(activityContext);
        // assert
        assertNotNull(result);
    }

    /**
     * Test successful case where ExtPoint is found and returns title
     */
    @Test
    public void testTitleSuccessful() throws Throwable {
        // arrange
        String expectedTitle = "Test Title";
        when(componentFinder.findExtPoint(any(ActivityContext.class), eq(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE))).thenReturn(productListBuilderExt);
        when(productListBuilderExt.title(activityContext)).thenReturn(expectedTitle);
        // act
        String result = defaultProductListBuilder.title(activityContext);
        // assert
        assertEquals(expectedTitle, result);
    }

    /**
     * Test case where ComponentNotFoundException is thrown
     */
    @Test
    public void testTitleComponentNotFound() throws Throwable {
        // arrange
        when(componentFinder.findExtPoint(any(ActivityContext.class), eq(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE))).thenThrow(new ComponentNotFoundException("Component not found"));
        // act
        String result = defaultProductListBuilder.title(activityContext);
        // assert
        assertNull(result);
        verify(activityContext).getSceneCode();
    }

    /**
     * Test case where ExtPoint is found but returns null title
     */
    @Test
    public void testTitleExtPointReturnsNull() throws Throwable {
        // arrange
        when(componentFinder.findExtPoint(any(ActivityContext.class), eq(ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE))).thenReturn(productListBuilderExt);
        when(productListBuilderExt.title(activityContext)).thenReturn(null);
        // act
        String result = defaultProductListBuilder.title(activityContext);
        // assert
        assertNull(result);
    }

    @Test
    public void testMoreJumpUrlSuccess() throws Throwable {
        String expectedUrl = "http://test.url";
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.moreJumpUrl(activityContext)).thenReturn(expectedUrl);
        String result = defaultProductListBuilder.moreJumpUrl(activityContext);
        assertEquals(expectedUrl, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).moreJumpUrl(activityContext);
    }

    @Test
    public void testMoreJumpUrlComponentNotFoundException() throws Throwable {
        when(activityContext.getSceneCode()).thenReturn("testScene");
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenThrow(new ComponentNotFoundException("Component not found"));
        String result = defaultProductListBuilder.moreJumpUrl(activityContext);
        assertNull(result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(activityContext).getSceneCode();
    }

    @Test
    public void testMoreJumpUrlReturnsNull() throws Throwable {
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.moreJumpUrl(activityContext)).thenReturn(null);
        String result = defaultProductListBuilder.moreJumpUrl(activityContext);
        assertNull(result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).moreJumpUrl(activityContext);
    }

    @Test
    public void testMoreJumpUrlWhenExtPointFound() throws Throwable {
        String expectedUrl = "http://test.url";
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.moreJumpUrl(activityContext)).thenReturn(expectedUrl);
        String result = defaultProductListBuilder.moreJumpUrl(activityContext);
        assertEquals(expectedUrl, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).moreJumpUrl(activityContext);
    }

    @Test
    public void testMoreJumpUrlWhenComponentNotFound() throws Throwable {
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenThrow(new ComponentNotFoundException("Component not found"));
        String result = defaultProductListBuilder.moreJumpUrl(activityContext);
        assertNull(result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
    }

    /**
     * Test when showType parameter is greater than 0
     * Should return the parameter value directly
     */
    @Test
    public void testShowType_WhenShowTypeParamGreaterThanZero() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 3);
        when(activityContext.getParameters()).thenReturn(parameters);
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(3, result);
        verify(componentFinder, never()).findExtPoint(any(), any());
    }

    /**
     * Test when showType parameter is 0 and ExtPoint returns valid value
     * Should return value from ExtPoint
     */
    @Test
    public void testShowType_WhenShowTypeZeroAndExtPointReturnsValue() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.showType(activityContext)).thenReturn(5);
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(5, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).showType(activityContext);
    }

    /**
     * Test when ComponentNotFoundException occurs
     * Should return 0 and report error
     */
    @Test
    public void testShowType_WhenComponentNotFoundExceptionOccurs() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenThrow(new ComponentNotFoundException("Component not found"));
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(0, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
    }

    /**
     * Test when general Exception occurs
     * Should return 0 and report error
     */
    @Test
    public void testShowType_WhenGeneralExceptionOccurs() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenThrow(new RuntimeException("Test exception"));
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(0, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
    }

    /**
     * Test when parameters map is null
     * Should return 0
     */
    @Test
    public void testShowType_WhenParametersMapIsNull() throws Throwable {
        // arrange
        when(activityContext.getParameters()).thenReturn(null);
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when showType parameter is missing
     * Should try to get value from ExtPoint
     */
    @Test
    public void testShowType_WhenShowTypeParameterIsMissing() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        when(activityContext.getParameters()).thenReturn(parameters);
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.showType(activityContext)).thenReturn(5);
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(5, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).showType(activityContext);
    }

    /**
     * Test case to verify showType when findExtPoint returns valid ExtPoint
     * Covers the code block:
     * return findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE).showType(activityContext);
     */
    @Test
    public void testShowType_WhenFindExtPointReturnsValidExtPoint() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(FilterListActivityConstants.Style.showType, 0);
        when(activityContext.getParameters()).thenReturn(parameters);
        when(componentFinder.findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE)).thenReturn(productListBuilderExt);
        when(productListBuilderExt.showType(activityContext)).thenReturn(5);
        // act
        int result = defaultProductListBuilder.showType(activityContext);
        // assert
        assertEquals(5, result);
        verify(componentFinder).findExtPoint(activityContext, ProductListBuilderExt.EXT_POINT_EXT_BUILDER_CODE);
        verify(productListBuilderExt).showType(activityContext);
    }
}
