package com.sankuai.dzviewscene.shelf.platform.shelf.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShelfErrorUtilsAddBuilderCatErrorTest {

    @Mock
    private ActivityContext ctx;

    @Mock
    private FilterBtnM filterBtnM;

    /**
     * 测试 addBuilderCatError 方法在正常情况下的行为
     */
    @Test
    public void testAddBuilderCatErrorNormal() throws Throwable {
        // arrange
        String productType = "productType";
        Exception exception = new Exception();
        when(ctx.getSceneCode()).thenReturn("sceneCode");
        // act
        ShelfErrorUtils.addBuilderCatError(ctx, productType, filterBtnM, exception);
        // assert
        // Since we cannot mock static methods, we assume the method is called correctly.
        // We can verify the interactions with the mocked objects.
        verify(ctx, times(1)).getSceneCode();
    }

    /**
     * 测试 addBuilderCatError 方法在异常情况下的行为
     */
    @Test
    public void testAddBuilderCatErrorException() throws Throwable {
        // arrange
        String productType = "productType";
        Exception exception = new Exception();
        when(ctx.getSceneCode()).thenReturn("sceneCode");
        // act
        try {
            ShelfErrorUtils.addBuilderCatError(ctx, productType, filterBtnM, exception);
        } catch (RuntimeException e) {
            // expected exception
        }
        // assert
        // Since we cannot mock static methods, we assume the method is called correctly.
        // We can verify the interactions with the mocked objects.
        verify(ctx, times(1)).getSceneCode();
    }
}
