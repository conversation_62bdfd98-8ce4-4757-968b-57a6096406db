package com.sankuai.dzviewscene.shelf.framework;

import com.sankuai.dzviewscene.shelf.SpringConfiguration;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutFuture;
import org.apache.shiro.util.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Ignore
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringConfiguration.class})
public class TimeoutFutureUnitTest {

    //@Test
    @DisplayName("测试超时功能正常运行")
    public void test_timeout() throws Exception {
        CompletableFuture<String> stringCompletableFuture = CompletableFuture.supplyAsync(() -> {
            try {
                Thread.sleep(4000);
            } catch (InterruptedException ignored) {
            }
            return "finish";
        });
        CompletableFuture<String> timeoutFuture = TimeoutFuture.of(stringCompletableFuture, 800, TimeUnit.MILLISECONDS, "timeout finish");
        Thread.sleep(1000);
        Assert.isTrue(timeoutFuture.isCompletedExceptionally());
    }

    //@Test
    @DisplayName("测试超时功能正常运行, 返回默认值")
    public void test_timeout_default() throws Exception {
        CompletableFuture<String> stringCompletableFuture = new CompletableFuture<>().thenApply(e -> "thenApply finished");
        TimeoutFuture<String> timeoutFuture = new TimeoutFuture<>(stringCompletableFuture)
                .onTimeoutWithDefault(1000, TimeUnit.MILLISECONDS, "not finish");
        Thread.sleep(1200);
        Assert.isTrue(timeoutFuture.isDone() && "not finish".equals(timeoutFuture.join()));
    }

    //@Test
    @DisplayName("测试最后一个future先完成的情况")
    public void test_last_future_complete_first() throws Exception {
        CompletableFuture<Integer> firstCf = new CompletableFuture<>();
        CompletableFuture<Integer> secondCf = firstCf.thenCompose(CompletableFuture::completedFuture);

        TimeoutFuture.of(firstCf, 2000, TimeUnit.MILLISECONDS, 1);
        TimeoutFuture.of(secondCf, 100, TimeUnit.MILLISECONDS, 4);
        Thread.sleep(500);
        Assert.isTrue(secondCf.join() == 4);
        Thread.sleep(2200);
        Assert.isTrue(secondCf.join() == 1);
    }

}
