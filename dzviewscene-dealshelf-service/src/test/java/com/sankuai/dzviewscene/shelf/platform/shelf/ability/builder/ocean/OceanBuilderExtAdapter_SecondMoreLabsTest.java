package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OceanBuilderExtAdapter_SecondMoreLabsTest {

    /**
     * 测试 secondMoreLabs 方法是否总是返回 null
     */
    @Test
    public void testSecondMoreLabsReturnNull() throws Throwable {
        // arrange
        OceanBuilderExtAdapter oceanBuilderExtAdapter = new OceanBuilderExtAdapter() {

            @Override
            public String secondMoreLabs(ActivityContext activityContext) {
                return null;
            }
        };
        ActivityContext activityContext = new ActivityContext();
        // act
        String result = oceanBuilderExtAdapter.secondMoreLabs(activityContext);
        // assert
        assertNull(result);
    }
}
