package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.assertEquals;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFloorsBuilderExt_FloorShowTypeTest {

    /**
     * 测试floorShowType方法是否能正确返回0
     */
    @Test
    public void testFloorShowTypeReturnZero() throws Throwable {
        // arrange
        ShelfFloorsBuilderExt shelfFloorsBuilderExt = new ShelfFloorsBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        ProductGroupM productGroupM = new ProductGroupM();
        // act
        int result = shelfFloorsBuilderExt.floorShowType(activityContext, groupName, productGroupM);
        // assert
        assertEquals(0, result);
    }
}
