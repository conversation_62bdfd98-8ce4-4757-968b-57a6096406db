package com.sankuai.dzviewscene.productshelf.filterlist.joypin;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterProductListVO;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Title: other
 * Description: 酒吧拼场广场测试
 *
 * <AUTHOR>
 * @date 2020-12-09
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com/sankuai/dzviewscene/shelf/business/filterlist/joypin","com.sankuai.dzviewscene", "com.sankuai.dzviewscene.nr.atom","com.sankuai.dzviewscene.productshelf.nr", "com.sankuai.athena","com.dianping.vc.sdk"})
public class JoyBarPinLandingFilterListActivityIntegTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
    }

    @Resource
    private ActivityEngine activityEngine;

    @Environment(value = AthenaEnv.Test, swimlane = "3792-efiep")
    //@Test
    public void test() {

        ActivityRequest activityRequest = buildActivityRequest();
        executeByActivityEngine(activityRequest)    ;

    }

    private ActivityRequest buildActivityRequest() {

        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(FilterListActivity.ACTIVITY_FILTER_LIST_CODE);
        activityRequest.setStartTime(System.currentTimeMillis());
        Map<String, Object> params = activityRequest.getParams();
        params.put(FilterListActivityConstants.Params.sceneCode, "joy_landing_city_pin_list");
        params.put(FilterListActivityConstants.Params.platform, 2);
        params.put(FilterListActivityConstants.Params.unionId, "3e9f3d51f9f14b39b39fa0328f097ba3a155600291719487415");
        params.put(FilterListActivityConstants.Params.lat, 30.0);
        params.put(FilterListActivityConstants.Params.lng, 120.0);
        params.put(FilterListActivityConstants.Params.joinNum, 0);
        params.put(FilterListActivityConstants.Params.mtUserId, 0L);
        params.put(FilterListActivityConstants.Params.mtCityId, 10);
        params.put(FilterListActivityConstants.Params.deviceId, "3e9f3d51f9f14b39b39fa0328f097ba3a155600291719487415");
       // params.put(FilterListActivityConstants.Params.dpCityId, 1);
        params.put(FilterListActivityConstants.Params.userAgent, VCClientTypeEnum.MT_APP.getCode());

        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzFilterProductListVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }
        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        DzFilterProductListVO carouselDTO = activityResponse.getResult().join();
        return carouselDTO;
    }



}
