package com.sankuai.dzviewscene.productshelf.shelf;

import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityRequest;
import com.dianping.gmkt.activity.api.dto.BatchQueryShopActivityResponse;
import com.dianping.gmkt.activity.api.dto.DealActivityDTO;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.framework.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 曝光活动降级集成测试
 *
 * <AUTHOR>
 * @date 2021/3/19 4:15 下午
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com/sankuai/dzviewscene/shelf/business/filterlist/joypin", "com.sankuai.dzviewscene", "com.sankuai.dzviewscene.nr.atom", "com.sankuai.dzviewscene.productshelf.nr", "com.sankuai.athena", "com.dianping.vc.sdk"})
public class ActivityContextExtDegradeIntegTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("compositeAtomService", new CompositeAtomServiceImpl() {
            @Override
            public CompletableFuture<BatchQueryShopActivityResponse> getShopActivity(BatchQueryShopActivityRequest request) {
                //模拟异常抛出
                int a = 1 / 0;
                return null;
            }
        }.getClass());
    }

    @Resource
    private ActivityContextExt activityContextExt;

    @Resource
    private ActivityEngine activityEngine;

    @Environment(value = AthenaEnv.Test)
    //@Test
    public void test_activityContextExt_test_environment_given_compositeAtomService_with_exception_return_dealActivityDTOList() {
        CompletableFuture<List<DealActivityDTO>> dealActivityDTOListCompletableFuture = activityContextExt.contextExt(buildActivityContext(1, 1, 122901734));
        Assert.assertNotNull(dealActivityDTOListCompletableFuture);
        Assert.assertTrue(CollectionUtils.isNotEmpty(dealActivityDTOListCompletableFuture.join()));
        Assert.assertNotNull(dealActivityDTOListCompletableFuture.join().get(0));
        Assert.assertTrue(CollectionUtils.isNotEmpty(dealActivityDTOListCompletableFuture.join().get(0).getTitles()));
    }

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test_activityContextExt_prod_environment_given_compositeAtomService_with_exception_return_dealActivityDTOList() {
        CompletableFuture<List<DealActivityDTO>> dealActivityDTOListCompletableFuture = activityContextExt.contextExt(buildActivityContext(1, 1, 122901734));
        Assert.assertNotNull(dealActivityDTOListCompletableFuture);
        Assert.assertTrue(CollectionUtils.isNotEmpty(dealActivityDTOListCompletableFuture.join()));
        Assert.assertNotNull(dealActivityDTOListCompletableFuture.join().get(0));
        Assert.assertTrue(CollectionUtils.isNotEmpty(dealActivityDTOListCompletableFuture.join().get(0).getTitles()));
    }

    private ActivityContext buildActivityContext(int platform, int cityId, int shopid) {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(new HashMap<String, Object>() {{
            put(ShelfActivityConstants.Params.platform, platform);
            put(ShelfActivityConstants.Params.dpPoiId, shopid);
            put(ShelfActivityConstants.Params.dpCityId, cityId);
        }});
        return activityContext;
    }

    @Environment(value = AthenaEnv.Product)
    //@Test
    public void test_beauty_prod_environment_link_given_compositeAtomService_with_exception_return_activityResponse() {
        ActivityRequest activityRequest = buildActivityRequest(100, 1, 122901734);
        ActivityResponse activityResponse = activityEngine.execute(activityRequest);
        Assert.assertNotNull(activityResponse);
    }

    private ActivityRequest buildActivityRequest(int platform, int cityId, int shopid) {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, cityId);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopid);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, cityId);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopid);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

}