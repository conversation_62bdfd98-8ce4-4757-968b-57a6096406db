package com.sankuai.dztheme.shelf.nr.atom;

import com.dianping.tpfun.product.api.common.IResponse;
import com.dianping.tpfun.product.api.stocklogic.dto.PeriodStockQueryRequest;
import com.dianping.tpfun.product.api.stocklogic.dto.StockDTO;
import com.dianping.tpfun.product.api.stocklogic.service.StockQueryService;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.testng.Assert;
import org.testng.collections.Maps;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/10/16.
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.xxxxx"})
public class StockQueryServiceTest {


    @RpcClient(url = "http://service.dianping.com/tpfunService/stockQueryService_1.0.0")
    private StockQueryService stockQueryService;


    //@Test
    @DisplayName("联调线上StockQueryService#queryPeriodStock服务")
    @Environment(env = AthenaEnv.Product, swimlane = "")
    public void test_query_stock() throws Exception {
        PeriodStockQueryRequest periodStockQueryRequest = buildPeriodStockQueryRequest();
        CompletableFuture<IResponse<List<StockDTO>>> responseCompletableFuture =
                AthenaInf.getRpcCompletableFuture(stockQueryService.queryPeriodStock(periodStockQueryRequest));
        Assert.assertTrue(responseCompletableFuture.join() != null);
    }

    private PeriodStockQueryRequest buildPeriodStockQueryRequest() {
        PeriodStockQueryRequest request = new PeriodStockQueryRequest();
        request.setSkuId(610318563);
        request.setServiceTimeMinutes(60);
        request.setShowTimeInterval(30);
        Map<String, String> params = Maps.newHashMap();
        params.put("shopid",String.valueOf(92044157));
        request.setParams(params);

        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            request.setBeginTime(parseDate(LocalDateTime.now().format(formatter)));
            request.setEndTime(parseDate(LocalDateTime.now().plusDays(3).format(formatter)));
        } catch (Exception e) {

        }
        return request;
    }

    public Date parseDate(String dateStr){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(dateStr);
        }catch (Exception e){
            return null;
        }
    }

}
