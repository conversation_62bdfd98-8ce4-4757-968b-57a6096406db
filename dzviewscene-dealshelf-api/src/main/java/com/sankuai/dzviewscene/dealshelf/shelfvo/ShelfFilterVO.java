/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShelfFilterVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfFilterVO implements Serializable {
    /**
     * 筛选项根节点
     */
    @MobileDo.MobileField
    private ShelfFilterNodeVO filterRoot;
}
