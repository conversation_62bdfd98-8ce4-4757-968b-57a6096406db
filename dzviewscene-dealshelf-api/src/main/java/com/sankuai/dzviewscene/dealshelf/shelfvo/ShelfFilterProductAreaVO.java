/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-07
 * Project        :
 * File Name      : ShelfFilterProductAreaVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfFilterProductAreaVO implements Serializable {
    /**
     * 商品区
     */
    @MobileDo.MobileField
    private List<ShelfProductAreaVO> productAreas;

    /**
     * 筛选项id
     */
    @MobileDo.MobileField
    private String filterId;

    // 只在结果返回的时候进行处理，处理一下 traceId 信息。
    private String traceId;

    // 保留字段，业务请勿使用，只在结果返回的时候进行处理
    private String retainMsg;

}
