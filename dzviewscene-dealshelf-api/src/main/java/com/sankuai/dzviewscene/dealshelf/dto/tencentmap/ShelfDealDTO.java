package com.sankuai.dzviewscene.dealshelf.dto.tencentmap;

import java.io.Serializable;

/**
 * 货架团购信息
 * <AUTHOR>
 * @date 2022/5/20
 */
public class ShelfDealDTO implements Serializable {
    /**
     * 团购id， 废弃，请使用itemIdL
     */
    @Deprecated
    private Integer itemId;

    /**
     * 团购id
     */
    private Long itemIdL;

    /**
     * 团购标题
     */
    private String title;

    /**
     * 团购售价（有优惠时为优惠价）
     */
    private String salePrice;

    /**
     * 市场价（有优惠时不返回）
     */
    private String marketPrice;

    /**
     * 销量信息，如半年消费30
     */
    private String sale;

    /**
     * 头图
     */
    private String picUrl;

    /**
     * 优惠标签，如已减20
     */
    private String promoTag;

    /**
     * 拼团价描述
     */
    private String pinPriceTag;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    @Deprecated
    public Integer getItemId() {
        return itemId;
    }

    @Deprecated
    public void setItemId(Integer itemId) {
        this.itemId = itemId;
    }

    public Long getItemIdL() {
        return itemIdL;
    }

    public void setItemIdL(Long itemIdL) {
        this.itemIdL = itemIdL;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(String salePrice) {
        this.salePrice = salePrice;
    }

    public String getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(String marketPrice) {
        this.marketPrice = marketPrice;
    }

    public String getSale() {
        return sale;
    }

    public void setSale(String sale) {
        this.sale = sale;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public String getPinPriceTag() {
        return pinPriceTag;
    }

    public void setPinPriceTag(String pinPriceTag) {
        this.pinPriceTag = pinPriceTag;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    @Override
    public String toString() {
        return "ShelfDealDTO{" +
                "itemId=" + itemId +
                ", title='" + title + '\'' +
                ", salePrice='" + salePrice + '\'' +
                ", marketPrice='" + marketPrice + '\'' +
                ", sale='" + sale + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", promoTag='" + promoTag + '\'' +
                ", pinPriceTag='" + pinPriceTag + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                '}';
    }
}
